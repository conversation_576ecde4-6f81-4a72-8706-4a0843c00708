<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hand Gesture Recognition System</title>
    <link rel="stylesheet" href="gesture-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-hand-paper"></i>
                <h1>Gesture Recognition AI</h1>
            </div>
            <div class="status-indicator">
                <span class="status-dot" id="statusDot"></span>
                <span class="status-text" id="statusText">Camera Off</span>
            </div>
        </header>

        <main class="main-content">

            <!-- Video Feed Section -->
            <section class="video-section">
                <div class="video-container">
                    <div class="video-header">
                        <h3><i class="fas fa-video"></i> Live Camera Feed</h3>
                        <div class="video-controls">
                            <button class="control-btn" id="cameraBtn">
                                <i class="fas fa-play" id="cameraIcon"></i>
                                <span id="cameraText">Start Camera</span>
                            </button>
                            <button class="control-btn secondary" id="captureBtn" disabled>
                                <i class="fas fa-camera"></i>
                                <span>Capture</span>
                            </button>
                        </div>
                    </div>

                    <div class="video-feed">
                        <video id="videoElement" autoplay muted playsinline></video>
                        <div class="video-placeholder" id="videoPlaceholder">
                            <i class="fas fa-video-slash"></i>
                            <p>Camera feed will appear here</p>
                            <small>Click "Start Camera" to begin</small>
                        </div>
                        <canvas id="overlayCanvas"></canvas>
                    </div>

                    <div class="video-info">
                        <div class="info-item">
                            <span class="label">Resolution:</span>
                            <span class="value" id="resolution">Not Available</span>
                        </div>
                        <div class="info-item">
                            <span class="label">FPS:</span>
                            <span class="value" id="fps">0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Gestures Detected:</span>
                            <span class="value" id="gestureCount">0</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recognition Results Section -->
            <section class="results-section">
                <div class="results-container">
                    <div class="results-header">
                        <h3><i class="fas fa-brain"></i> Recognition Results</h3>
                        <div class="results-controls">
                            <button class="control-btn secondary" id="clearBtn">
                                <i class="fas fa-trash"></i>
                                <span>Clear</span>
                            </button>
                            <button class="control-btn secondary" id="copyBtn">
                                <i class="fas fa-copy"></i>
                                <span>Copy</span>
                            </button>
                        </div>
                    </div>

                    <div class="recognized-text">
                        <div class="text-display" id="recognizedText">
                            <div class="placeholder-message">
                                <i class="fas fa-hand-point-right"></i>
                                <p>Recognized gestures and text will appear here</p>
                                <small>Start the camera and make hand gestures to see results</small>
                            </div>
                        </div>
                    </div>

                    <div class="confidence-meter">
                        <label>Recognition Confidence:</label>
                        <div class="meter-container">
                            <div class="meter-bar">
                                <div class="meter-fill" id="confidenceFill"></div>
                            </div>
                            <span class="meter-value" id="confidenceValue">0%</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="gesture-script.js"></script>
</body>
</html>

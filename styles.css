/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #667eea 50%, #764ba2 75%, #f093fb 100%);
    background-size: 400% 400%, 300% 300%, 200% 200%, 400% 400%;
    animation: gradientShift 20s ease infinite;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow-x: hidden;
    position: relative;
}

/* Professional geometric background pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(30deg, rgba(255, 255, 255, 0.02) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.02) 87.5%, rgba(255, 255, 255, 0.02)),
        linear-gradient(150deg, rgba(255, 255, 255, 0.02) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.02) 87.5%, rgba(255, 255, 255, 0.02)),
        linear-gradient(30deg, rgba(255, 255, 255, 0.02) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.02) 87.5%, rgba(255, 255, 255, 0.02)),
        linear-gradient(150deg, rgba(255, 255, 255, 0.02) 12%, transparent 12.5%, transparent 87%, rgba(255, 255, 255, 0.02) 87.5%, rgba(255, 255, 255, 0.02)),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    background-size: 80px 140px, 80px 140px, 40px 70px, 40px 70px, 200px 200px;
    background-position: 0 0, 0 0, 40px 70px, 40px 70px, 0 0;
    pointer-events: none;
    z-index: 0;
    animation: patternMove 30s linear infinite;
}

/* Corporate grid overlay */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: 0;
    animation: gridFloat 25s ease-in-out infinite;
}

@keyframes patternMove {
    0% { transform: translate(0, 0); }
    25% { transform: translate(-20px, -20px); }
    50% { transform: translate(20px, -40px); }
    75% { transform: translate(-10px, 20px); }
    100% { transform: translate(0, 0); }
}

@keyframes gridFloat {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.1; transform: scale(1.05); }
}

/* Floating Particles Animation */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 50%;
    animation: float 20s infinite linear;
    box-shadow:
        0 0 20px rgba(255, 255, 255, 0.1),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(1px);
}

.particle:nth-child(1) {
    width: 20px;
    height: 20px;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 25s;
}

.particle:nth-child(2) {
    width: 15px;
    height: 15px;
    left: 20%;
    animation-delay: 2s;
    animation-duration: 20s;
}

.particle:nth-child(3) {
    width: 25px;
    height: 25px;
    left: 30%;
    animation-delay: 4s;
    animation-duration: 30s;
}

.particle:nth-child(4) {
    width: 18px;
    height: 18px;
    left: 40%;
    animation-delay: 6s;
    animation-duration: 22s;
}

.particle:nth-child(5) {
    width: 12px;
    height: 12px;
    left: 60%;
    animation-delay: 8s;
    animation-duration: 18s;
}

.particle:nth-child(6) {
    width: 22px;
    height: 22px;
    left: 70%;
    animation-delay: 10s;
    animation-duration: 28s;
}

.particle:nth-child(7) {
    width: 16px;
    height: 16px;
    left: 80%;
    animation-delay: 12s;
    animation-duration: 24s;
}

.particle:nth-child(8) {
    width: 14px;
    height: 14px;
    left: 90%;
    animation-delay: 14s;
    animation-duration: 26s;
}

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.container {
    width: 100%;
    max-width: 450px;
    animation: slideInUp 0.8s ease-out;
    position: relative;
    z-index: 1;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 40px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Professional corner accents */
.auth-container::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), transparent);
    border-radius: 0 24px 0 100px;
    pointer-events: none;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.auth-container:hover {
    box-shadow:
        0 35px 70px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* Professional decorative elements */
.corner-decoration {
    position: absolute;
    width: 60px;
    height: 60px;
    pointer-events: none;
    z-index: 1;
}

.corner-decoration.top-left {
    top: -1px;
    left: -1px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), transparent 70%);
    border-radius: 24px 0 60px 0;
    animation: cornerPulse 4s ease-in-out infinite;
}

.corner-decoration.bottom-right {
    bottom: -1px;
    right: -1px;
    background: linear-gradient(315deg, rgba(118, 75, 162, 0.2), transparent 70%);
    border-radius: 0 60px 0 24px;
    animation: cornerPulse 4s ease-in-out infinite 2s;
}

.side-accent {
    position: absolute;
    width: 3px;
    height: 40px;
    background: linear-gradient(180deg, transparent, rgba(102, 126, 234, 0.3), transparent);
    pointer-events: none;
    z-index: 1;
    animation: accentGlow 3s ease-in-out infinite;
}

.side-accent.left {
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 0 3px 3px 0;
}

.side-accent.right {
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 3px 0 0 3px;
    animation-delay: 1.5s;
}

/* Logo styling */
.logo-placeholder {
    margin-bottom: 20px;
    animation: logoFloat 3s ease-in-out infinite;
}

.logo-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 10px 30px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: logoShine 2s ease-in-out infinite;
}

.logo-icon i {
    font-size: 24px;
    color: white;
    z-index: 1;
}

@keyframes cornerPulse {
    0%, 100% { opacity: 0.2; transform: scale(1); }
    50% { opacity: 0.4; transform: scale(1.1); }
}

@keyframes accentGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes logoShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
    animation: fadeInDown 1s ease-out 0.3s both;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header h1 {
    color: #333;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from { filter: brightness(1); }
    to { filter: brightness(1.2); }
}

.auth-header p {
    color: #666;
    font-size: 1rem;
    animation: fadeIn 1s ease-out 0.6s both;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Tab Navigation */
.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 4px;
    animation: slideInLeft 0.8s ease-out 0.4s both;
    position: relative;
    overflow: hidden;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.tab-btn:hover::before {
    left: 100%;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    transform: scale(1.02);
    animation: tabPulse 0.3s ease-out;
}

@keyframes tabPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1.02); }
}

/* Form Styles */
.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
    animation: formSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes formSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.form-group {
    margin-bottom: 20px;
    animation: slideInRight 0.6s ease-out;
    animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }
.form-group:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.input-group i {
    position: absolute;
    left: 15px;
    color: #999;
    z-index: 2;
    transition: all 0.3s ease;
}

.input-group input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    position: relative;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.input-group input:focus + .input-group i,
.input-group input:focus ~ i {
    color: #667eea;
    transform: scale(1.1);
}

.input-group:hover {
    transform: translateY(-1px);
}

.input-group:hover input {
    border-color: #667eea;
}

.password-toggle {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 5px;
    z-index: 2;
    transition: all 0.3s ease;
    border-radius: 50%;
}

.password-toggle:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.1);
}

.password-toggle:active {
    transform: scale(0.95);
}

/* Error Messages */
.error-message {
    display: block;
    color: #e74c3c;
    font-size: 0.85rem;
    margin-top: 5px;
    min-height: 20px;
    transition: all 0.3s ease;
    transform: translateY(-5px);
    opacity: 0;
}

.error-message:not(:empty) {
    opacity: 1;
    transform: translateY(0);
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 8px;
}

.strength-bar {
    height: 4px;
    background: #e1e5e9;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.strength-bar::after {
    content: '';
    display: block;
    height: 100%;
    width: 0%;
    background: #e74c3c;
    transition: all 0.3s ease;
}

.strength-bar.weak::after { width: 25%; background: #e74c3c; }
.strength-bar.fair::after { width: 50%; background: #f39c12; }
.strength-bar.good::after { width: 75%; background: #f1c40f; }
.strength-bar.strong::after { width: 100%; background: #27ae60; }

.strength-text {
    font-size: 0.8rem;
    color: #666;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 10px;
}

/* Custom Checkbox */
.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e1e5e9;
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #5a6fd8;
}

/* Submit Button */
.auth-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 200% 200%;
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
    animation: buttonSlideUp 0.6s ease-out 0.8s both;
}

@keyframes buttonSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.auth-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    background-position: 100% 0;
}

.auth-btn:hover::before {
    left: 100%;
}

.auth-btn:active {
    transform: translateY(-1px);
    transition: transform 0.1s;
}

.auth-btn i {
    transition: transform 0.3s ease;
}

.auth-btn:hover i {
    transform: translateX(5px);
}

/* Links */
a {
    color: #667eea;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 480px) {
    .auth-container {
        padding: 30px 20px;
        margin: 10px;
    }

    .auth-header h1 {
        font-size: 2rem;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}

/* Success/Loading States */
.auth-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.auth-btn.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.input-group.success input {
    border-color: #27ae60;
}

.input-group.error input {
    border-color: #e74c3c;
}

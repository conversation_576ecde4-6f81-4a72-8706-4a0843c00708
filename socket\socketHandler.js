const { authenticateSocket } = require('../middleware/auth');
const { handleSocketError } = require('../middleware/errorHandler');
const Room = require('../models/Room');
const Call = require('../models/Call');
const User = require('../models/User');

// Store active connections
const activeConnections = new Map();
const roomConnections = new Map();

const socketHandler = (io) => {
    // Authentication middleware for socket connections
    io.use(authenticateSocket);

    io.on('connection', (socket) => {
        console.log(`User ${socket.user.name} connected: ${socket.id}`);

        // Store user connection
        activeConnections.set(socket.user._id.toString(), {
            socketId: socket.id,
            user: socket.user,
            joinedAt: new Date()
        });

        // Update user status to online
        updateUserStatus(socket.user._id, 'online');

        // Handle joining a room
        socket.on('join-room', async (data) => {
            try {
                const { roomId, role = 'participant' } = data;

                // Validate room exists
                const room = await Room.findOne({ roomId }).populate('participants.user', 'name profile.avatar');
                if (!room) {
                    socket.emit('error', { message: 'Room not found' });
                    return;
                }

                // Check if room is full
                if (room.activeParticipantsCount >= room.settings.maxParticipants) {
                    socket.emit('error', { message: 'Room is full' });
                    return;
                }

                // Add user to room
                await room.addParticipant(socket.user._id, role);

                // Join socket room
                socket.join(roomId);

                // Store room connection
                if (!roomConnections.has(roomId)) {
                    roomConnections.set(roomId, new Map());
                }
                roomConnections.get(roomId).set(socket.user._id.toString(), socket.id);

                // Notify others in the room
                socket.to(roomId).emit('user-joined', {
                    user: socket.user.getPublicProfile(),
                    role: role,
                    timestamp: new Date()
                });

                // Send room data to user
                socket.emit('room-joined', {
                    room: room,
                    participants: room.participants,
                    timestamp: new Date()
                });

                console.log(`User ${socket.user.name} joined room ${roomId}`);

            } catch (error) {
                console.error('Join room error:', error);
                handleSocketError(socket, error);
            }
        });

        // Handle leaving a room
        socket.on('leave-room', async (data) => {
            try {
                const { roomId } = data;

                // Remove user from room
                const room = await Room.findOne({ roomId });
                if (room) {
                    await room.removeParticipant(socket.user._id);
                }

                // Leave socket room
                socket.leave(roomId);

                // Remove from room connections
                if (roomConnections.has(roomId)) {
                    roomConnections.get(roomId).delete(socket.user._id.toString());
                    if (roomConnections.get(roomId).size === 0) {
                        roomConnections.delete(roomId);
                    }
                }

                // Notify others in the room
                socket.to(roomId).emit('user-left', {
                    user: socket.user.getPublicProfile(),
                    timestamp: new Date()
                });

                console.log(`User ${socket.user.name} left room ${roomId}`);

            } catch (error) {
                console.error('Leave room error:', error);
                handleSocketError(socket, error);
            }
        });

        // Handle WebRTC signaling
        socket.on('webrtc-offer', (data) => {
            const { roomId, targetUserId, offer } = data;
            
            // Forward offer to target user
            const targetConnection = getConnectionInRoom(roomId, targetUserId);
            if (targetConnection) {
                io.to(targetConnection).emit('webrtc-offer', {
                    fromUserId: socket.user._id,
                    offer: offer
                });
            }
        });

        socket.on('webrtc-answer', (data) => {
            const { roomId, targetUserId, answer } = data;
            
            // Forward answer to target user
            const targetConnection = getConnectionInRoom(roomId, targetUserId);
            if (targetConnection) {
                io.to(targetConnection).emit('webrtc-answer', {
                    fromUserId: socket.user._id,
                    answer: answer
                });
            }
        });

        socket.on('webrtc-ice-candidate', (data) => {
            const { roomId, targetUserId, candidate } = data;
            
            // Forward ICE candidate to target user
            const targetConnection = getConnectionInRoom(roomId, targetUserId);
            if (targetConnection) {
                io.to(targetConnection).emit('webrtc-ice-candidate', {
                    fromUserId: socket.user._id,
                    candidate: candidate
                });
            }
        });

        // Handle chat messages
        socket.on('send-message', async (data) => {
            try {
                const { roomId, content, type = 'text' } = data;

                // Validate room
                const room = await Room.findOne({ roomId });
                if (!room) {
                    socket.emit('error', { message: 'Room not found' });
                    return;
                }

                // Add message to room
                await room.addMessage(socket.user._id, content, type);

                // Broadcast message to room
                io.to(roomId).emit('new-message', {
                    sender: socket.user.getPublicProfile(),
                    content: content,
                    type: type,
                    timestamp: new Date()
                });

            } catch (error) {
                console.error('Send message error:', error);
                handleSocketError(socket, error);
            }
        });

        // Handle sign language recognition
        socket.on('gesture-detected', async (data) => {
            try {
                const { roomId, gestureData, confidence, translation } = data;

                // Validate room
                const room = await Room.findOne({ roomId });
                if (!room) {
                    socket.emit('error', { message: 'Room not found' });
                    return;
                }

                // Add transcription to room
                await room.addTranscription(socket.user._id, translation, confidence, 'sign');

                // Broadcast gesture detection to room
                socket.to(roomId).emit('gesture-recognized', {
                    user: socket.user.getPublicProfile(),
                    gestureData: gestureData,
                    translation: translation,
                    confidence: confidence,
                    timestamp: new Date()
                });

            } catch (error) {
                console.error('Gesture detection error:', error);
                handleSocketError(socket, error);
            }
        });

        // Handle call quality updates
        socket.on('quality-update', async (data) => {
            try {
                const { roomId, qualityMetrics } = data;

                // Find active call
                const call = await Call.findOne({ 
                    room: roomId, 
                    status: 'connected' 
                });

                if (call) {
                    await call.updateQualityMetrics(socket.user._id, qualityMetrics);
                }

                // Broadcast quality update to room (for monitoring)
                socket.to(roomId).emit('quality-update', {
                    user: socket.user._id,
                    metrics: qualityMetrics,
                    timestamp: new Date()
                });

            } catch (error) {
                console.error('Quality update error:', error);
                handleSocketError(socket, error);
            }
        });

        // Handle typing indicators
        socket.on('typing-start', (data) => {
            const { roomId } = data;
            socket.to(roomId).emit('user-typing', {
                user: socket.user.getPublicProfile(),
                isTyping: true
            });
        });

        socket.on('typing-stop', (data) => {
            const { roomId } = data;
            socket.to(roomId).emit('user-typing', {
                user: socket.user.getPublicProfile(),
                isTyping: false
            });
        });

        // Handle status updates
        socket.on('status-update', async (data) => {
            try {
                const { status } = data;
                
                if (['online', 'away', 'busy'].includes(status)) {
                    await updateUserStatus(socket.user._id, status);
                    
                    // Broadcast status update to contacts/friends
                    socket.broadcast.emit('user-status-changed', {
                        userId: socket.user._id,
                        status: status,
                        timestamp: new Date()
                    });
                }

            } catch (error) {
                console.error('Status update error:', error);
                handleSocketError(socket, error);
            }
        });

        // Handle disconnection
        socket.on('disconnect', async (reason) => {
            try {
                console.log(`User ${socket.user.name} disconnected: ${reason}`);

                // Remove from active connections
                activeConnections.delete(socket.user._id.toString());

                // Remove from all room connections
                for (const [roomId, connections] of roomConnections.entries()) {
                    if (connections.has(socket.user._id.toString())) {
                        connections.delete(socket.user._id.toString());
                        
                        // Notify room about user disconnection
                        socket.to(roomId).emit('user-disconnected', {
                            user: socket.user.getPublicProfile(),
                            timestamp: new Date()
                        });

                        // Update room participant status
                        const room = await Room.findOne({ roomId });
                        if (room) {
                            await room.removeParticipant(socket.user._id, 'left');
                        }

                        // Clean up empty room connections
                        if (connections.size === 0) {
                            roomConnections.delete(roomId);
                        }
                    }
                }

                // Update user status to offline after a delay
                setTimeout(async () => {
                    const stillConnected = activeConnections.has(socket.user._id.toString());
                    if (!stillConnected) {
                        await updateUserStatus(socket.user._id, 'offline');
                    }
                }, 5000); // 5 second grace period for reconnection

            } catch (error) {
                console.error('Disconnect error:', error);
            }
        });

        // Handle errors
        socket.on('error', (error) => {
            console.error('Socket error:', error);
            handleSocketError(socket, error);
        });
    });

    // Helper functions
    function getConnectionInRoom(roomId, userId) {
        const roomConns = roomConnections.get(roomId);
        return roomConns ? roomConns.get(userId.toString()) : null;
    }

    async function updateUserStatus(userId, status) {
        try {
            await User.findByIdAndUpdate(userId, { 
                status: status,
                lastActive: new Date()
            });
        } catch (error) {
            console.error('Failed to update user status:', error);
        }
    }

    // Periodic cleanup of stale connections
    setInterval(() => {
        const now = Date.now();
        for (const [userId, connection] of activeConnections.entries()) {
            const timeSinceJoin = now - connection.joinedAt.getTime();
            // Remove connections older than 1 hour without activity
            if (timeSinceJoin > 60 * 60 * 1000) {
                activeConnections.delete(userId);
            }
        }
    }, 5 * 60 * 1000); // Run every 5 minutes
};

module.exports = socketHandler;

const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const roomSchema = new mongoose.Schema({
    roomId: {
        type: String,
        unique: true,
        default: () => uuidv4(),
        required: true
    },
    name: {
        type: String,
        required: [true, 'Room name is required'],
        trim: true,
        maxlength: [100, 'Room name cannot be more than 100 characters']
    },
    description: {
        type: String,
        maxlength: [500, 'Description cannot be more than 500 characters']
    },
    type: {
        type: String,
        enum: ['public', 'private', 'scheduled'],
        default: 'private',
        required: true
    },
    host: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    participants: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        role: {
            type: String,
            enum: ['host', 'interpreter', 'participant'],
            default: 'participant'
        },
        joinedAt: {
            type: Date,
            default: Date.now
        },
        leftAt: Date,
        status: {
            type: String,
            enum: ['joined', 'left', 'kicked', 'banned'],
            default: 'joined'
        }
    }],
    settings: {
        maxParticipants: {
            type: Number,
            default: 10,
            min: 2,
            max: 50
        },
        requirePassword: {
            type: Boolean,
            default: false
        },
        password: {
            type: String,
            select: false
        },
        allowRecording: {
            type: Boolean,
            default: false
        },
        autoTranscription: {
            type: Boolean,
            default: true
        },
        languages: [{
            type: String,
            enum: ['ASL', 'BSL', 'ISL', 'JSL', 'CSL', 'FSL', 'GSL', 'Other']
        }],
        videoQuality: {
            type: String,
            enum: ['low', 'medium', 'high', 'auto'],
            default: 'auto'
        }
    },
    schedule: {
        startTime: Date,
        endTime: Date,
        timezone: {
            type: String,
            default: 'UTC'
        },
        recurring: {
            type: String,
            enum: ['none', 'daily', 'weekly', 'monthly'],
            default: 'none'
        }
    },
    status: {
        type: String,
        enum: ['waiting', 'active', 'ended', 'cancelled'],
        default: 'waiting'
    },
    callData: {
        startedAt: Date,
        endedAt: Date,
        duration: Number, // in seconds
        recordingUrl: String,
        transcription: [{
            timestamp: Date,
            speaker: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            text: String,
            confidence: Number,
            type: {
                type: String,
                enum: ['speech', 'sign', 'text'],
                default: 'sign'
            }
        }],
        qualityMetrics: {
            averageLatency: Number,
            packetLoss: Number,
            videoQuality: String,
            audioQuality: String
        }
    },
    messages: [{
        sender: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        content: {
            type: String,
            required: true,
            maxlength: [1000, 'Message cannot be more than 1000 characters']
        },
        type: {
            type: String,
            enum: ['text', 'gesture', 'system'],
            default: 'text'
        },
        timestamp: {
            type: Date,
            default: Date.now
        },
        edited: {
            type: Boolean,
            default: false
        },
        editedAt: Date
    }],
    inviteLinks: [{
        token: {
            type: String,
            unique: true,
            default: () => uuidv4()
        },
        expiresAt: {
            type: Date,
            default: () => new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        },
        usageLimit: {
            type: Number,
            default: 1
        },
        usedCount: {
            type: Number,
            default: 0
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        }
    }]
}, {
    timestamps: true
});

// Indexes for better performance
roomSchema.index({ roomId: 1 });
roomSchema.index({ host: 1 });
roomSchema.index({ 'participants.user': 1 });
roomSchema.index({ status: 1 });
roomSchema.index({ 'schedule.startTime': 1 });
roomSchema.index({ type: 1 });

// Virtual for active participants count
roomSchema.virtual('activeParticipantsCount').get(function() {
    return this.participants.filter(p => p.status === 'joined').length;
});

// Virtual for room duration
roomSchema.virtual('duration').get(function() {
    if (this.callData.startedAt && this.callData.endedAt) {
        return Math.floor((this.callData.endedAt - this.callData.startedAt) / 1000);
    }
    return 0;
});

// Method to add participant
roomSchema.methods.addParticipant = function(userId, role = 'participant') {
    const existingParticipant = this.participants.find(
        p => p.user.toString() === userId.toString() && p.status === 'joined'
    );
    
    if (existingParticipant) {
        throw new Error('User is already in the room');
    }
    
    if (this.activeParticipantsCount >= this.settings.maxParticipants) {
        throw new Error('Room is full');
    }
    
    this.participants.push({
        user: userId,
        role: role,
        joinedAt: new Date(),
        status: 'joined'
    });
    
    return this.save();
};

// Method to remove participant
roomSchema.methods.removeParticipant = function(userId, reason = 'left') {
    const participant = this.participants.find(
        p => p.user.toString() === userId.toString() && p.status === 'joined'
    );
    
    if (participant) {
        participant.status = reason;
        participant.leftAt = new Date();
    }
    
    return this.save();
};

// Method to start call
roomSchema.methods.startCall = function() {
    this.status = 'active';
    this.callData.startedAt = new Date();
    return this.save();
};

// Method to end call
roomSchema.methods.endCall = function() {
    this.status = 'ended';
    this.callData.endedAt = new Date();
    this.callData.duration = Math.floor(
        (this.callData.endedAt - this.callData.startedAt) / 1000
    );
    
    // Mark all joined participants as left
    this.participants.forEach(participant => {
        if (participant.status === 'joined') {
            participant.status = 'left';
            participant.leftAt = new Date();
        }
    });
    
    return this.save();
};

// Method to add message
roomSchema.methods.addMessage = function(senderId, content, type = 'text') {
    this.messages.push({
        sender: senderId,
        content: content,
        type: type,
        timestamp: new Date()
    });
    
    return this.save();
};

// Method to add transcription
roomSchema.methods.addTranscription = function(speakerId, text, confidence = 1.0, type = 'sign') {
    this.callData.transcription.push({
        timestamp: new Date(),
        speaker: speakerId,
        text: text,
        confidence: confidence,
        type: type
    });
    
    return this.save();
};

// Method to generate invite link
roomSchema.methods.generateInviteLink = function(createdBy, expiresIn = 24, usageLimit = 1) {
    const expiresAt = new Date(Date.now() + expiresIn * 60 * 60 * 1000);
    
    this.inviteLinks.push({
        token: uuidv4(),
        expiresAt: expiresAt,
        usageLimit: usageLimit,
        usedCount: 0,
        createdBy: createdBy
    });
    
    return this.save();
};

// Method to validate invite link
roomSchema.methods.validateInviteLink = function(token) {
    const invite = this.inviteLinks.find(link => link.token === token);
    
    if (!invite) {
        throw new Error('Invalid invite link');
    }
    
    if (invite.expiresAt < new Date()) {
        throw new Error('Invite link has expired');
    }
    
    if (invite.usedCount >= invite.usageLimit) {
        throw new Error('Invite link usage limit exceeded');
    }
    
    return invite;
};

// Method to use invite link
roomSchema.methods.useInviteLink = function(token) {
    const invite = this.validateInviteLink(token);
    invite.usedCount += 1;
    return this.save();
};

// Static method to find public rooms
roomSchema.statics.findPublicRooms = function(languages = []) {
    const query = {
        type: 'public',
        status: { $in: ['waiting', 'active'] }
    };
    
    if (languages.length > 0) {
        query['settings.languages'] = { $in: languages };
    }
    
    return this.find(query)
        .populate('host', 'name profile.avatar')
        .populate('participants.user', 'name profile.avatar')
        .sort({ createdAt: -1 });
};

// Static method to find user's rooms
roomSchema.statics.findUserRooms = function(userId) {
    return this.find({
        $or: [
            { host: userId },
            { 'participants.user': userId }
        ]
    })
    .populate('host', 'name profile.avatar')
    .populate('participants.user', 'name profile.avatar')
    .sort({ updatedAt: -1 });
};

module.exports = mongoose.model('Room', roomSchema);

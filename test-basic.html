<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        #video {
            width: 100%;
            max-width: 400px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 20px 0;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            background: #e9ecef;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Basic Camera Test</h1>
        <p>Let's test if your camera works first!</p>
        
        <button onclick="startCamera()">Start Camera</button>
        <button onclick="stopCamera()">Stop Camera</button>
        
        <div class="status" id="status">
            Click "Start Camera" to test your camera
        </div>
        
        <video id="video" autoplay muted playsinline style="display: none;"></video>
    </div>

    <script>
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        let stream = null;

        async function startCamera() {
            try {
                status.textContent = 'Requesting camera access...';
                
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: true 
                });
                
                video.srcObject = stream;
                video.style.display = 'block';
                status.textContent = '✅ Camera is working! You should see yourself above.';
                status.style.background = '#d4edda';
                status.style.borderLeftColor = '#28a745';
                
            } catch (error) {
                status.textContent = '❌ Camera error: ' + error.message;
                status.style.background = '#f8d7da';
                status.style.borderLeftColor = '#dc3545';
                console.error('Camera error:', error);
            }
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            video.style.display = 'none';
            status.textContent = 'Camera stopped. Click "Start Camera" to test again.';
            status.style.background = '#e9ecef';
            status.style.borderLeftColor = '#007bff';
        }

        console.log('Basic camera test ready!');
    </script>
</body>
</html>

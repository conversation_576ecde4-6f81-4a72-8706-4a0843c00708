#!/usr/bin/env python3
"""
Simple Hand Gesture Recognition System
Works reliably on localhost:3000
"""

import cv2
import numpy as np
import base64
from flask import Flask, render_template_string
from flask_socketio import Socket<PERSON>, emit
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'simple_gesture_key'
socketio = SocketIO(app, cors_allowed_origins="*")

class SimpleGestureRecognizer:
    def __init__(self):
        self.gestures = ['Stop', '1', '2', 'Hello', 'Good']
        
    def detect_gesture(self, frame):
        """FAST gesture detection - optimized for speed"""
        try:
            # Resize frame for faster processing
            small_frame = cv2.resize(frame, (160, 120))

            # Simple HSV skin detection only
            hsv = cv2.cvtColor(small_frame, cv2.COLOR_BGR2HSV)

            # Fast skin mask
            lower_skin = np.array([0, 20, 70], dtype=np.uint8)
            upper_skin = np.array([20, 255, 255], dtype=np.uint8)
            mask = cv2.inRange(hsv, lower_skin, upper_skin)

            # Minimal cleanup
            kernel = np.ones((3,3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return None, 0.0

            # Get largest contour
            max_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(max_contour)

            # Scale area back to original size
            scale_factor = (frame.shape[0] * frame.shape[1]) / (160 * 120)
            area = area * scale_factor

            if area < 1000:
                return None, 0.0

            # Fast classification based on area only
            if area > 15000:
                return "Hello", 0.85  # Large = open hand
            elif area > 8000:
                return "Stop", 0.90   # Medium-large = fist
            elif area > 4000:
                return "2", 0.80      # Medium = two fingers
            elif area > 2000:
                return "1", 0.75      # Small-medium = one finger
            else:
                return "Good", 0.70   # Small = partial hand

        except Exception as e:
            return None, 0.0

    def classify_gesture_enhanced(self, area, solidity, finger_count, aspect_ratio):
        """Enhanced gesture classification"""
        try:
            # Very solid shape = closed fist
            if solidity > 0.8 and area > 1000:
                return "Stop", 0.95

            # Large area with fingers = open hand
            if area > 3000 and finger_count >= 3:
                return "Hello", 0.90

            # Medium area with specific finger counts
            if area > 1500:
                if finger_count == 1:
                    return "1", 0.85
                elif finger_count == 2:
                    return "2", 0.85
                elif finger_count >= 3:
                    return "Hello", 0.80

            # Any reasonable hand shape
            if area > 800:
                return "Good", 0.75

            return None, 0.0

        except Exception as e:
            print(f"Classification error: {e}")
            return None, 0.0

# Initialize recognizer
recognizer = SimpleGestureRecognizer()

@app.route('/')
def index():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>🤲 Simple Gesture Recognition</title>
    <style>
        body { 
            font-family: 'Segoe UI', Arial, sans-serif; 
            margin: 0; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            align-items: start;
        }
        .video-section {
            background: #000;
            border-radius: 20px;
            overflow: hidden;
            aspect-ratio: 16/9;
            max-height: 450px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        #video { 
            width: 100%; 
            height: 100%; 
            object-fit: cover;
        }
        .controls {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        button {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .start { background: #4CAF50; color: white; }
        .start:hover { background: #45a049; transform: translateY(-2px); }
        .stop { background: #f44336; color: white; }
        .stop:hover { background: #da190b; transform: translateY(-2px); }
        .gesture-display {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
        }
        .gesture-text {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .confidence {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: 600;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }
        @media (max-width: 768px) {
            .main-content { grid-template-columns: 1fr; }
            .video-section { max-height: 300px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤲 Simple Gesture Recognition</h1>
            <p>Real-time hand gesture detection that actually works!</p>
        </div>
        
        <div class="main-content">
            <div class="video-section">
                <video id="video" autoplay></video>
            </div>
            
            <div class="controls">
                <button onclick="startCamera()" class="start">📹 Start Camera</button>
                <button onclick="startRecognition()" class="start" id="gestureBtn" disabled>🤲 Start Recognition</button>
                <button onclick="stopAll()" class="stop">🛑 Stop All</button>
                
                <div id="status" class="status">Ready to start</div>
                
                <div class="gesture-display">
                    <div class="gesture-text" id="gestureText">No gesture</div>
                    <div class="confidence" id="confidence">Show your hand to the camera</div>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="gestureCount">0</div>
                        <div class="stat-label">Gestures</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="accuracy">0%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        const socket = io();
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        const gestureText = document.getElementById('gestureText');
        const confidence = document.getElementById('confidence');
        const gestureBtn = document.getElementById('gestureBtn');
        const gestureCount = document.getElementById('gestureCount');
        const accuracy = document.getElementById('accuracy');
        
        let stream = null;
        let isRecognizing = false;
        let totalGestures = 0;
        let successfulGestures = 0;
        
        function updateStatus(message, type = 'success') {
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        async function startCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { width: 640, height: 480 } 
                });
                video.srcObject = stream;
                gestureBtn.disabled = false;
                updateStatus('📹 Camera started successfully', 'success');
            } catch (error) {
                updateStatus('❌ Camera access denied', 'error');
            }
        }
        
        function startRecognition() {
            if (!stream) {
                updateStatus('❌ Start camera first', 'error');
                return;
            }
            
            isRecognizing = true;
            gestureBtn.textContent = '🛑 Stop Recognition';
            gestureBtn.onclick = stopRecognition;
            updateStatus('🤲 Gesture recognition active', 'success');
            sendFrames();
        }
        
        function stopRecognition() {
            isRecognizing = false;
            gestureBtn.textContent = '🤲 Start Recognition';
            gestureBtn.onclick = startRecognition;
            updateStatus('⏹️ Recognition stopped', 'success');
        }
        
        function stopAll() {
            isRecognizing = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            gestureBtn.disabled = true;
            updateStatus('🛑 All stopped', 'success');
        }
        
        function sendFrames() {
            if (!isRecognizing || !stream) return;
            
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            ctx.drawImage(video, 0, 0);
            const imageData = canvas.toDataURL('image/jpeg', 0.5);
            
            socket.emit('process_frame', { image: imageData });
            setTimeout(sendFrames, 100); // Process every 100ms for speed
        }
        
        function updateStats() {
            gestureCount.textContent = totalGestures;
            const acc = totalGestures > 0 ? Math.round((successfulGestures / totalGestures) * 100) : 0;
            accuracy.textContent = acc + '%';
        }
        
        socket.on('connect', function() {
            updateStatus('🔗 Connected to server', 'success');
        });
        
        socket.on('disconnect', function() {
            updateStatus('❌ Connection lost', 'error');
        });
        
        socket.on('gesture_result', function(data) {
            if (data.gesture && data.confidence > 0.6) {
                totalGestures++;
                successfulGestures++;
                
                gestureText.textContent = data.gesture;
                confidence.textContent = `Confidence: ${Math.round(data.confidence * 100)}%`;
                
                updateStats();
                console.log('Gesture detected:', data.gesture);
            } else {
                gestureText.textContent = 'Looking for hand...';
                confidence.textContent = 'Show your hand clearly';
            }
        });
    </script>
</body>
</html>
    ''')

@socketio.on('process_frame')
def process_frame(data):
    """Process video frame and detect gestures"""
    try:
        # Decode image
        image_data = data['image'].split(',')[1]
        image_bytes = base64.b64decode(image_data)
        nparr = np.frombuffer(image_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if frame is None:
            emit('gesture_result', {'gesture': None, 'confidence': 0})
            return
        
        # Detect gesture
        gesture, confidence = recognizer.detect_gesture(frame)
        
        # Send result
        emit('gesture_result', {
            'gesture': gesture,
            'confidence': confidence
        })
        
    except Exception as e:
        print(f"Error: {e}")
        emit('gesture_result', {'gesture': None, 'confidence': 0})

if __name__ == '__main__':
    print("🚀 Starting Simple Gesture Recognition Server...")
    print("📡 Server running on http://localhost:3000")
    print("🤲 Open browser and start making gestures!")
    socketio.run(app, host='0.0.0.0', port=3000, debug=False)

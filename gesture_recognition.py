#!/usr/bin/env python3
"""
Hand Gesture Recognition Server
Uses MediaPipe and OpenCV for real-time gesture detection
"""

import cv2
import mediapipe as mp
import numpy as np
import json
import base64
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
import threading
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'gesture_recognition_secret'
socketio = SocketIO(app, cors_allowed_origins="*", logger=True, engineio_logger=True)

# Initialize MediaPipe
mp_hands = mp.solutions.hands
mp_drawing = mp.solutions.drawing_utils
hands = mp_hands.Hands(
    static_image_mode=False,
    max_num_hands=2,
    min_detection_confidence=0.7,
    min_tracking_confidence=0.5
)

class GestureRecognizer:
    def __init__(self):
        self.gesture_names = {
            'fist': 'Stop',
            'one': '1',
            'two': '2',
            'three': '3',
            'four': '4',
            'five': 'Hello',
            'thumbs_up': 'Good',
            'peace': 'Peace',
            'ok': 'OK'
        }

    def count_extended_fingers(self, landmarks):
        """Count how many fingers are extended"""
        # Finger tip and joint indices
        finger_tips = [4, 8, 12, 16, 20]  # thumb, index, middle, ring, pinky
        finger_joints = [3, 6, 10, 14, 18]

        extended_fingers = 0

        # Check thumb (different logic due to orientation)
        if landmarks[4].x > landmarks[3].x:  # thumb tip right of joint
            extended_fingers += 1

        # Check other fingers
        for i in range(1, 5):
            if landmarks[finger_tips[i]].y < landmarks[finger_joints[i]].y:
                extended_fingers += 1

        return extended_fingers

    def classify_gesture(self, landmarks):
        """Classify gesture based on hand landmarks"""
        if not landmarks:
            return None

        extended_fingers = self.count_extended_fingers(landmarks)

        # Get key landmarks
        thumb_tip = landmarks[4]
        index_tip = landmarks[8]
        middle_tip = landmarks[12]
        wrist = landmarks[0]

        # Classify gestures
        if extended_fingers == 0:
            return 'Stop'
        elif extended_fingers == 1:
            if index_tip.y < landmarks[6].y:  # index finger up
                return '1'
            elif thumb_tip.y < wrist.y:  # thumb up
                return 'Good'
        elif extended_fingers == 2:
            # Check for peace sign (index and middle up)
            if (index_tip.y < landmarks[6].y and
                middle_tip.y < landmarks[10].y and
                landmarks[16].y > landmarks[14].y):  # ring finger down
                return '2'
        elif extended_fingers == 3:
            return '3'
        elif extended_fingers == 4:
            return '4'
        elif extended_fingers == 5:
            return 'Hello'

        return 'Unknown'

gesture_recognizer = GestureRecognizer()

@app.route('/')
def index():
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>Python Gesture Recognition</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; }
        h1 { text-align: center; color: #333; }
        #video { width: 100%; max-width: 640px; border: 2px solid #ddd; border-radius: 10px; }
        .controls { text-align: center; margin: 20px 0; }
        button { background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; margin: 10px; font-size: 16px; }
        button:hover { background: #0056b3; }
        button:disabled { opacity: 0.5; cursor: not-allowed; }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; background: #e9ecef; text-align: center; }
        .gesture-display { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0; text-align: center; }
        .gesture-text { font-size: 24px; font-weight: bold; color: #155724; }
        .confidence { font-size: 14px; color: #666; margin-top: 10px; }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <h1>🤲 Python Hand Gesture Recognition</h1>

        <video id="video" autoplay muted playsinline></video>

        <div class="controls">
            <button onclick="startCamera()">Start Camera</button>
            <button onclick="startGestureRecognition()" id="gestureBtn" disabled>Start Gesture Recognition</button>
            <button onclick="stopAll()">Stop</button>
        </div>

        <div class="status" id="status">Click "Start Camera" to begin</div>

        <div class="gesture-display" id="gestureDisplay" style="display: none;">
            <div class="gesture-text" id="gestureText">No gesture detected</div>
            <div class="confidence" id="confidence">Confidence: 0%</div>
        </div>

        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; padding: 20px; margin: 20px 0;">
            <h3>👋 Supported Gestures:</h3>
            <ul>
                <li>✊ <strong>Closed Fist</strong> → "Stop"</li>
                <li>☝️ <strong>One Finger</strong> → "1"</li>
                <li>✌️ <strong>Two Fingers</strong> → "2"</li>
                <li>🤟 <strong>Three Fingers</strong> → "3"</li>
                <li>🖐️ <strong>Open Hand</strong> → "Hello"</li>
                <li>👍 <strong>Thumbs Up</strong> → "Good"</li>
            </ul>
        </div>
    </div>

    <script>
        const socket = io();
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        const gestureDisplay = document.getElementById('gestureDisplay');
        const gestureText = document.getElementById('gestureText');
        const confidence = document.getElementById('confidence');
        const gestureBtn = document.getElementById('gestureBtn');

        let stream = null;
        let isRecognizing = false;

        async function startCamera() {
            try {
                status.textContent = 'Starting camera...';
                stream = await navigator.mediaDevices.getUserMedia({ video: true });
                video.srcObject = stream;

                video.onloadedmetadata = () => {
                    status.textContent = 'Camera started! Click "Start Gesture Recognition"';
                    gestureBtn.disabled = false;
                };
            } catch (error) {
                status.textContent = 'Error: Could not access camera - ' + error.message;
            }
        }

        function startGestureRecognition() {
            if (!stream) {
                status.textContent = 'Please start camera first!';
                return;
            }

            isRecognizing = true;
            gestureDisplay.style.display = 'block';
            status.textContent = 'Gesture recognition active - Show your hand!';
            gestureBtn.textContent = 'Stop Gesture Recognition';
            gestureBtn.onclick = stopGestureRecognition;

            // Start sending frames to Python backend
            sendFrames();
        }

        function stopGestureRecognition() {
            isRecognizing = false;
            gestureDisplay.style.display = 'none';
            status.textContent = 'Gesture recognition stopped';
            gestureBtn.textContent = 'Start Gesture Recognition';
            gestureBtn.onclick = startGestureRecognition;
        }

        function sendFrames() {
            if (!isRecognizing) return;

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            ctx.drawImage(video, 0, 0);
            const imageData = canvas.toDataURL('image/jpeg', 0.8);

            socket.emit('process_frame', { image: imageData });

            setTimeout(sendFrames, 200); // Send frame every 200ms
        }

        function stopAll() {
            isRecognizing = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            gestureDisplay.style.display = 'none';
            status.textContent = 'Stopped. Click "Start Camera" to begin again.';
            gestureBtn.disabled = true;
            gestureBtn.textContent = 'Start Gesture Recognition';
            gestureBtn.onclick = startGestureRecognition;
        }

        // Listen for gesture results from Python backend
        socket.on('gesture_result', function(data) {
            if (data.gesture) {
                gestureText.textContent = data.gesture;
                confidence.textContent = `Confidence: ${Math.round(data.confidence * 100)}%`;
            } else {
                gestureText.textContent = 'No hand detected';
                confidence.textContent = 'Show your hand to the camera';
            }
        });

        socket.on('connect', function() {
            console.log('Connected to Python backend!');
        });
    </script>
</body>
</html>
    '''

@socketio.on('process_frame')
def process_frame(data):
    """Process video frame and detect gestures"""
    try:
        # Decode base64 image
        image_data = data['image'].split(',')[1]
        image_bytes = base64.b64decode(image_data)

        # Convert to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if frame is None:
            emit('gesture_result', {'gesture': None, 'confidence': 0})
            return

        # Convert BGR to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Process with MediaPipe
        results = hands.process(rgb_frame)

        if results.multi_hand_landmarks:
            # Get first hand
            hand_landmarks = results.multi_hand_landmarks[0]

            # Classify gesture
            gesture = gesture_recognizer.classify_gesture(hand_landmarks.landmark)
            confidence = 0.9  # MediaPipe doesn't provide confidence, so we use a default

            emit('gesture_result', {
                'gesture': gesture,
                'confidence': confidence
            })
        else:
            emit('gesture_result', {'gesture': None, 'confidence': 0})

    except Exception as e:
        print(f"Error processing frame: {e}")
        emit('gesture_result', {'gesture': None, 'confidence': 0})

if __name__ == '__main__':
    print("🚀 Starting Python Gesture Recognition Server...")
    print("📱 Install required packages: pip install flask flask-socketio opencv-python mediapipe")
    print("🌐 Open: http://localhost:5001")
    socketio.run(app, host='0.0.0.0', port=5001, debug=True)

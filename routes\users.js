const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { asyncHandler, handleValidationError } = require('../middleware/errorHandler');
const { authorize, requireVerification } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/users/profile
// @desc    Get current user profile
// @access  Private
router.get('/profile', asyncHandler(async (req, res) => {
    const user = await User.findById(req.user._id)
        .populate('callHistory', 'callId duration startTime endTime status');

    res.json({
        success: true,
        data: {
            user: user.getPublicProfile()
        }
    });
}));

// @route   PUT /api/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', [
    body('name')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Name must be between 2 and 50 characters'),
    body('profile.bio')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Bio cannot be more than 500 characters'),
    body('profile.languages')
        .optional()
        .isArray()
        .withMessage('Languages must be an array'),
    body('profile.experience')
        .optional()
        .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
        .withMessage('Experience must be beginner, intermediate, advanced, or expert')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json(handleValidationError(errors));
    }

    const { name, profile, preferences } = req.body;

    const user = await User.findById(req.user._id);

    // Update fields
    if (name) user.name = name;
    if (profile) {
        user.profile = { ...user.profile, ...profile };
    }
    if (preferences) {
        user.preferences = { ...user.preferences, ...preferences };
    }

    await user.save();

    res.json({
        success: true,
        message: 'Profile updated successfully',
        data: {
            user: user.getPublicProfile()
        }
    });
}));

// @route   PUT /api/users/status
// @desc    Update user status
// @access  Private
router.put('/status', [
    body('status')
        .isIn(['online', 'offline', 'busy', 'away'])
        .withMessage('Status must be online, offline, busy, or away')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json(handleValidationError(errors));
    }

    const { status } = req.body;

    const user = await User.findByIdAndUpdate(
        req.user._id,
        { status, lastActive: new Date() },
        { new: true }
    );

    res.json({
        success: true,
        message: 'Status updated successfully',
        data: {
            status: user.status,
            lastActive: user.lastActive
        }
    });
}));

// @route   GET /api/users/interpreters
// @desc    Get available interpreters
// @access  Private
router.get('/interpreters', asyncHandler(async (req, res) => {
    const { languages, experience, page = 1, limit = 10 } = req.query;

    const languageFilter = languages ? languages.split(',') : [];
    
    let query = {
        role: 'interpreter',
        status: { $in: ['online', 'away'] },
        isVerified: true
    };

    if (languageFilter.length > 0) {
        query['profile.languages'] = { $in: languageFilter };
    }

    if (experience) {
        query['profile.experience'] = experience;
    }

    // Pagination
    const skip = (page - 1) * limit;

    const interpreters = await User.find(query)
        .select('-password -verificationToken -passwordResetToken')
        .sort({ lastActive: -1 })
        .skip(skip)
        .limit(parseInt(limit));

    const total = await User.countDocuments(query);

    res.json({
        success: true,
        data: {
            interpreters,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));

// @route   GET /api/users/:userId
// @desc    Get user profile by ID
// @access  Private
router.get('/:userId', asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await User.findById(userId)
        .select('-password -verificationToken -passwordResetToken -loginAttempts -lockUntil');

    if (!user) {
        return res.status(404).json({
            success: false,
            message: 'User not found'
        });
    }

    res.json({
        success: true,
        data: {
            user: user.getPublicProfile()
        }
    });
}));

// @route   POST /api/users/search
// @desc    Search users
// @access  Private
router.post('/search', [
    body('query')
        .trim()
        .isLength({ min: 2 })
        .withMessage('Search query must be at least 2 characters'),
    body('filters.role')
        .optional()
        .isIn(['interpreter', 'sign_user'])
        .withMessage('Role must be interpreter or sign_user'),
    body('filters.languages')
        .optional()
        .isArray()
        .withMessage('Languages must be an array')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json(handleValidationError(errors));
    }

    const { query, filters = {}, page = 1, limit = 10 } = req.body;

    // Build search query
    const searchQuery = {
        $and: [
            {
                $or: [
                    { name: { $regex: query, $options: 'i' } },
                    { email: { $regex: query, $options: 'i' } },
                    { 'profile.bio': { $regex: query, $options: 'i' } }
                ]
            },
            { isVerified: true }
        ]
    };

    if (filters.role) {
        searchQuery.$and.push({ role: filters.role });
    }

    if (filters.languages && filters.languages.length > 0) {
        searchQuery.$and.push({ 'profile.languages': { $in: filters.languages } });
    }

    if (filters.status) {
        searchQuery.$and.push({ status: filters.status });
    }

    // Pagination
    const skip = (page - 1) * limit;

    const users = await User.find(searchQuery)
        .select('-password -verificationToken -passwordResetToken')
        .sort({ lastActive: -1 })
        .skip(skip)
        .limit(parseInt(limit));

    const total = await User.countDocuments(searchQuery);

    res.json({
        success: true,
        data: {
            users: users.map(user => user.getPublicProfile()),
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));

// @route   PUT /api/users/preferences
// @desc    Update user preferences
// @access  Private
router.put('/preferences', [
    body('videoQuality')
        .optional()
        .isIn(['low', 'medium', 'high', 'auto'])
        .withMessage('Video quality must be low, medium, high, or auto'),
    body('notifications.email')
        .optional()
        .isBoolean()
        .withMessage('Email notifications must be boolean'),
    body('notifications.push')
        .optional()
        .isBoolean()
        .withMessage('Push notifications must be boolean'),
    body('privacy.showOnline')
        .optional()
        .isBoolean()
        .withMessage('Show online status must be boolean')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json(handleValidationError(errors));
    }

    const preferences = req.body;

    const user = await User.findById(req.user._id);
    user.preferences = { ...user.preferences, ...preferences };
    await user.save();

    res.json({
        success: true,
        message: 'Preferences updated successfully',
        data: {
            preferences: user.preferences
        }
    });
}));

// @route   GET /api/users/stats/dashboard
// @desc    Get user dashboard statistics
// @access  Private
router.get('/stats/dashboard', asyncHandler(async (req, res) => {
    const userId = req.user._id;
    const { period = '30' } = req.query; // days

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Get call statistics
    const Call = require('../models/Call');
    const callStats = await Call.getCallStatistics(userId, startDate, new Date());

    // Get room statistics
    const Room = require('../models/Room');
    const roomStats = await Room.aggregate([
        {
            $match: {
                $or: [
                    { host: userId },
                    { 'participants.user': userId }
                ],
                createdAt: { $gte: startDate }
            }
        },
        {
            $group: {
                _id: null,
                totalRooms: { $sum: 1 },
                hostedRooms: {
                    $sum: {
                        $cond: [{ $eq: ['$host', userId] }, 1, 0]
                    }
                }
            }
        }
    ]);

    res.json({
        success: true,
        data: {
            period: parseInt(period),
            calls: callStats[0] || {
                totalCalls: 0,
                totalDuration: 0,
                averageDuration: 0,
                successfulCalls: 0,
                totalCost: 0
            },
            rooms: roomStats[0] || {
                totalRooms: 0,
                hostedRooms: 0
            }
        }
    });
}));

// @route   DELETE /api/users/account
// @desc    Delete user account
// @access  Private
router.delete('/account', [
    body('password')
        .notEmpty()
        .withMessage('Password is required to delete account')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json(handleValidationError(errors));
    }

    const { password } = req.body;

    const user = await User.findById(req.user._id).select('+password');

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
        return res.status(401).json({
            success: false,
            message: 'Invalid password'
        });
    }

    // Delete user account
    await User.findByIdAndDelete(req.user._id);

    // TODO: Clean up user's rooms, calls, and other related data

    res.json({
        success: true,
        message: 'Account deleted successfully'
    });
}));

module.exports = router;

#!/usr/bin/env python3
"""
Debug version of gesture recognition to identify issues
"""

import cv2
import numpy as np
import base64
from flask import Flask, render_template_string
from flask_socketio import SocketIO, emit
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'debug_gesture_key'
socketio = SocketIO(app, cors_allowed_origins="*")

class DebugGestureRecognizer:
    def __init__(self):
        self.frame_count = 0
        self.detection_count = 0
        
    def detect_gesture(self, frame):
        """Debug version with detailed logging"""
        try:
            self.frame_count += 1
            print(f"🔍 Processing frame {self.frame_count}")
            
            if frame is None:
                print("❌ Frame is None")
                return None, 0.0
                
            print(f"📏 Frame shape: {frame.shape}")
            
            # Convert to different color spaces for skin detection
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            ycrcb = cv2.cvtColor(frame, cv2.COLOR_BGR2YCrCb)
            
            # Define skin color ranges (more permissive for debugging)
            hsv_lower = np.array([0, 20, 70], dtype=np.uint8)
            hsv_upper = np.array([20, 255, 255], dtype=np.uint8)
            
            ycrcb_lower = np.array([0, 135, 85], dtype=np.uint8)
            ycrcb_upper = np.array([255, 180, 135], dtype=np.uint8)
            
            # Create skin masks
            mask_hsv = cv2.inRange(hsv, hsv_lower, hsv_upper)
            mask_ycrcb = cv2.inRange(ycrcb, ycrcb_lower, ycrcb_upper)
            
            # Combine masks
            skin_mask = cv2.bitwise_and(mask_hsv, mask_ycrcb)
            
            # Count skin pixels
            skin_pixels = cv2.countNonZero(skin_mask)
            print(f"🖐️ Skin pixels detected: {skin_pixels}")
            
            if skin_pixels < 1000:
                print("⚠️ Not enough skin pixels detected")
                return None, 0.0
            
            # Apply morphological operations
            kernel = np.ones((3,3), np.uint8)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(skin_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                print("❌ No contours found")
                return None, 0.0
                
            print(f"📐 Found {len(contours)} contours")
            
            # Find largest contour (hand)
            max_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(max_contour)
            
            print(f"📏 Largest contour area: {area}")
            
            if area < 2000:
                print("⚠️ Contour too small")
                return None, 0.0
            
            # Simple gesture classification based on area and shape
            self.detection_count += 1
            
            if area > 15000:
                gesture = "Hello"
                confidence = 0.85
            elif area > 8000:
                gesture = "Stop"
                confidence = 0.80
            elif area > 5000:
                gesture = "2"
                confidence = 0.75
            else:
                gesture = "1"
                confidence = 0.70
                
            print(f"✅ Detected: {gesture} (confidence: {confidence:.2f})")
            return gesture, confidence
            
        except Exception as e:
            print(f"❌ Error in gesture detection: {e}")
            return None, 0.0

# Initialize recognizer
debug_recognizer = DebugGestureRecognizer()

@app.route('/')
def index():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>🔍 Debug Gesture Recognition</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        video { width: 100%; max-width: 640px; border: 2px solid #333; border-radius: 10px; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; border: none; border-radius: 5px; cursor: pointer; }
        .start { background: #4CAF50; color: white; }
        .stop { background: #f44336; color: white; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .debug { background: #e2e3e5; color: #383d41; font-family: monospace; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Gesture Recognition</h1>
        <video id="video" autoplay></video>
        <br>
        <button onclick="startCamera()" class="start">📹 Start Camera</button>
        <button onclick="startRecognition()" class="start" id="gestureBtn" disabled>🤲 Start Recognition</button>
        <button onclick="stopAll()" class="stop">🛑 Stop All</button>
        
        <div id="status" class="status">Ready to start</div>
        <div id="gesture" style="font-size: 24px; text-align: center; margin: 20px 0;">No gesture detected</div>
        <div id="debug" class="debug">Debug info will appear here...</div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        const socket = io();
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        const gestureDiv = document.getElementById('gesture');
        const debugDiv = document.getElementById('debug');
        const gestureBtn = document.getElementById('gestureBtn');
        
        let stream = null;
        let isRecognizing = false;
        
        function updateStatus(message, type = 'success') {
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function updateDebug(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        async function startCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { width: 640, height: 480 } 
                });
                video.srcObject = stream;
                gestureBtn.disabled = false;
                updateStatus('📹 Camera started successfully', 'success');
                updateDebug('Camera initialized');
            } catch (error) {
                updateStatus('❌ Camera access denied', 'error');
                updateDebug(`Camera error: ${error.message}`);
            }
        }
        
        function startRecognition() {
            if (!stream) {
                updateStatus('❌ Start camera first', 'error');
                return;
            }
            
            isRecognizing = true;
            gestureBtn.textContent = '🛑 Stop Recognition';
            gestureBtn.onclick = stopRecognition;
            updateStatus('🤲 Gesture recognition started', 'success');
            updateDebug('Starting gesture recognition...');
            sendFrames();
        }
        
        function stopRecognition() {
            isRecognizing = false;
            gestureBtn.textContent = '🤲 Start Recognition';
            gestureBtn.onclick = startRecognition;
            updateStatus('⏹️ Recognition stopped', 'success');
            updateDebug('Gesture recognition stopped');
        }
        
        function stopAll() {
            isRecognizing = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            gestureBtn.disabled = true;
            updateStatus('🛑 All stopped', 'success');
            updateDebug('All systems stopped');
        }
        
        function sendFrames() {
            if (!isRecognizing || !stream) return;
            
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            ctx.drawImage(video, 0, 0);
            const imageData = canvas.toDataURL('image/jpeg', 0.8);
            
            updateDebug('Sending frame to server...');
            socket.emit('process_frame', { image: imageData });
            
            setTimeout(sendFrames, 500); // Send every 500ms for debugging
        }
        
        socket.on('connect', function() {
            updateStatus('🔗 Connected to debug server', 'success');
            updateDebug('Connected to server');
        });
        
        socket.on('disconnect', function() {
            updateStatus('❌ Disconnected from server', 'error');
            updateDebug('Disconnected from server');
        });
        
        socket.on('gesture_result', function(data) {
            updateDebug(`Received result: ${JSON.stringify(data)}`);
            if (data.gesture && data.confidence > 0.5) {
                gestureDiv.textContent = `${data.gesture} (${Math.round(data.confidence * 100)}%)`;
                updateDebug(`Gesture detected: ${data.gesture}`);
            } else {
                gestureDiv.textContent = 'No gesture detected';
            }
        });
        
        updateDebug('Debug system initialized');
    </script>
</body>
</html>
    ''')

@socketio.on('process_frame')
def process_frame(data):
    """Process video frame with debug output"""
    try:
        print("📡 Received frame from client")
        
        # Decode base64 image
        image_data = data['image'].split(',')[1]
        image_bytes = base64.b64decode(image_data)
        
        # Convert to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if frame is None:
            print("❌ Failed to decode frame")
            emit('gesture_result', {'gesture': None, 'confidence': 0})
            return
        
        # Detect gesture
        gesture, confidence = debug_recognizer.detect_gesture(frame)
        
        print(f"📤 Sending result: {gesture}, {confidence}")
        emit('gesture_result', {
            'gesture': gesture,
            'confidence': confidence
        })
        
    except Exception as e:
        print(f"❌ Error processing frame: {e}")
        emit('gesture_result', {'gesture': None, 'confidence': 0})

@socketio.on('connect')
def handle_connect():
    print("✅ Client connected")

@socketio.on('disconnect')
def handle_disconnect():
    print("❌ Client disconnected")

if __name__ == '__main__':
    print("🔍 Starting Debug Gesture Recognition Server...")
    print("📡 Server will run on http://localhost:3001")
    print("🖥️ Open browser and check console for detailed debug info")
    socketio.run(app, host='0.0.0.0', port=3001, debug=True)

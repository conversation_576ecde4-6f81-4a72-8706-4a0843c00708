/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2rem;
    color: #667eea;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e74c3c;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: #27ae60;
}

.status-text {
    font-weight: 600;
    color: #666;
}

/* Main Content Layout */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-bottom: 30px;
}

.video-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.video-header h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #333;
}

.video-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.control-btn.secondary {
    background: #6c757d;
}

.video-feed {
    position: relative;
    width: 100%;
    height: 400px;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
}

#videoElement {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: none;
}

.video-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    text-align: center;
}

.video-placeholder i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.7;
}

.video-placeholder p {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.video-placeholder small {
    opacity: 0.8;
}

#overlayCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.video-info {
    display: flex;
    justify-content: space-around;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.info-item {
    text-align: center;
}

.info-item .label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}

.info-item .value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

/* Results Section */

.results-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.results-header h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #333;
}

.results-controls {
    display: flex;
    gap: 10px;
}

.recognized-text {
    min-height: 200px;
    margin-bottom: 20px;
}

.text-display {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.text-display.active {
    border-color: #667eea;
    background: #f0f4ff;
}

.placeholder-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 160px;
    text-align: center;
    color: #666;
}

.placeholder-message i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.confidence-meter {
    display: flex;
    align-items: center;
    gap: 15px;
}

.confidence-meter label {
    font-weight: 600;
    color: #333;
}

.meter-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.meter-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.meter-fill {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60);
    width: 0%;
    transition: width 0.3s ease;
}

.meter-value {
    font-weight: 600;
    color: #333;
    min-width: 40px;
}



/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .video-header,
    .results-header {
        flex-direction: column;
        gap: 15px;
    }
}

/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_BUFFERREUSEPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// BufferReusePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_BUFFERREUSEPASS
#undef GEN_PASS_DECL_BUFFERREUSEPASS
#endif // GEN_PASS_DECL_BUFFERREUSEPASS
#ifdef GEN_PASS_DEF_BUFFERREUSEPASS
namespace impl {

template <typename DerivedT>
class BufferReusePassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = BufferReusePassBase;

  BufferReusePassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferReusePassBase(const BufferReusePassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  BufferReusePassBase& operator=(const BufferReusePassBase &) = delete;
  BufferReusePassBase(BufferReusePassBase &&) = delete;
  BufferReusePassBase& operator=(BufferReusePassBase &&) = delete;
  ~BufferReusePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-buffer-reuse");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-buffer-reuse"; }

  ::llvm::StringRef getDescription() const override { return "Reuse buffers."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferReusePass");
  }
  ::llvm::StringRef getName() const override { return "BufferReusePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferReusePassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_BUFFERREUSEPASS
#endif // GEN_PASS_DEF_BUFFERREUSEPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// BufferReusePass Registration
//===----------------------------------------------------------------------===//

inline void registerBufferReusePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::deallocation::createBufferReusePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerBufferReusePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::deallocation::createBufferReusePass();
  });
}

//===----------------------------------------------------------------------===//
// Deallocation Registration
//===----------------------------------------------------------------------===//

inline void registerDeallocationPasses() {
  registerBufferReusePass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class BufferReusePassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = BufferReusePassBase;

  BufferReusePassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BufferReusePassBase(const BufferReusePassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  BufferReusePassBase& operator=(const BufferReusePassBase &) = delete;
  BufferReusePassBase(BufferReusePassBase &&) = delete;
  BufferReusePassBase& operator=(BufferReusePassBase &&) = delete;
  ~BufferReusePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-buffer-reuse");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-buffer-reuse"; }

  ::llvm::StringRef getDescription() const override { return "Reuse buffers."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BufferReusePass");
  }
  ::llvm::StringRef getName() const override { return "BufferReusePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<::mlir::memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BufferReusePassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES

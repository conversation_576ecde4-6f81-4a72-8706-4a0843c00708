<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Language Call - Server Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
        }
        .status-card h3 {
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-card .value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .status-card .description {
            color: #666;
            font-size: 0.9rem;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            display: inline-block;
            animation: pulse 2s infinite;
        }
        .endpoints {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        .endpoints h3 {
            margin-bottom: 20px;
            color: #333;
        }
        .endpoint {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }
        .endpoint .method {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .endpoint .method.post {
            background: #27ae60;
        }
        .endpoint .url {
            font-family: 'Courier New', monospace;
            color: #333;
            flex: 1;
            margin: 0 15px;
        }
        .endpoint .description {
            color: #666;
            font-size: 0.9rem;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        .feature-card .icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        .feature-card h4 {
            color: #333;
            margin-bottom: 10px;
        }
        .feature-card p {
            color: #666;
            font-size: 0.9rem;
        }
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤝 Sign Language Call Backend</h1>
            <p>Real-time video calling system with gesture recognition</p>
            <div style="margin-top: 15px;">
                <span class="status-indicator"></span>
                <span style="margin-left: 10px; color: #27ae60; font-weight: 600;">Server Running</span>
            </div>
        </div>

        <button class="refresh-btn" onclick="refreshStatus()">🔄 Refresh Status</button>

        <div class="status-grid">
            <div class="status-card">
                <h3>🌐 Server Status</h3>
                <div class="value" id="serverStatus">Online</div>
                <div class="description">Backend server is running on port 5000</div>
            </div>
            
            <div class="status-card">
                <h3>👥 Active Users</h3>
                <div class="value" id="activeUsers">0</div>
                <div class="description">Currently connected users</div>
            </div>
            
            <div class="status-card">
                <h3>🏠 Active Rooms</h3>
                <div class="value" id="activeRooms">0</div>
                <div class="description">Video call rooms in use</div>
            </div>
            
            <div class="status-card">
                <h3>📞 Active Calls</h3>
                <div class="value" id="activeCalls">0</div>
                <div class="description">Ongoing video calls</div>
            </div>
            
            <div class="status-card">
                <h3>🔌 Socket Connections</h3>
                <div class="value" id="socketConnections">0</div>
                <div class="description">Real-time WebSocket connections</div>
            </div>
            
            <div class="status-card">
                <h3>⏱️ Uptime</h3>
                <div class="value" id="uptime">0s</div>
                <div class="description">Server running time</div>
            </div>
        </div>

        <div class="endpoints">
            <h3>📡 Available API Endpoints</h3>
            
            <div class="endpoint">
                <span class="method">GET</span>
                <span class="url">/health</span>
                <span class="description">Server health check</span>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span>
                <span class="url">/api/status</span>
                <span class="description">Current server statistics</span>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/demo/user</span>
                <span class="description">Create demo user</span>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/demo/room</span>
                <span class="description">Create demo room</span>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span>
                <span class="url">/api/demo/rooms</span>
                <span class="description">List all demo rooms</span>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span>
                <span class="url">/test-client.html</span>
                <span class="description">WebSocket test client</span>
            </div>
        </div>

        <div class="features">
            <div class="feature-card">
                <div class="icon">🎥</div>
                <h4>Video Calling</h4>
                <p>WebRTC-based real-time video communication with signaling server</p>
            </div>
            
            <div class="feature-card">
                <div class="icon">👋</div>
                <h4>Gesture Recognition</h4>
                <p>Real-time sign language detection and translation capabilities</p>
            </div>
            
            <div class="feature-card">
                <div class="icon">💬</div>
                <h4>Live Messaging</h4>
                <p>Instant messaging with Socket.IO for real-time communication</p>
            </div>
            
            <div class="feature-card">
                <div class="icon">🏠</div>
                <h4>Room Management</h4>
                <p>Create and manage video call rooms with participant controls</p>
            </div>
            
            <div class="feature-card">
                <div class="icon">🔒</div>
                <h4>Secure Authentication</h4>
                <p>JWT-based authentication with role-based access control</p>
            </div>
            
            <div class="feature-card">
                <div class="icon">📊</div>
                <h4>Quality Monitoring</h4>
                <p>Real-time connection quality metrics and performance tracking</p>
            </div>
        </div>
    </div>

    <script>
        async function refreshStatus() {
            try {
                // Get health status
                const healthResponse = await fetch('/health');
                const healthData = await healthResponse.json();
                
                // Get API status
                const statusResponse = await fetch('/api/status');
                const statusData = await statusResponse.json();
                
                // Update UI
                document.getElementById('serverStatus').textContent = healthData.status;
                document.getElementById('activeUsers').textContent = statusData.data.users;
                document.getElementById('activeRooms').textContent = statusData.data.rooms;
                document.getElementById('activeCalls').textContent = statusData.data.calls;
                document.getElementById('socketConnections').textContent = statusData.data.activeConnections;
                
                // Format uptime
                const uptimeSeconds = Math.floor(healthData.uptime);
                const hours = Math.floor(uptimeSeconds / 3600);
                const minutes = Math.floor((uptimeSeconds % 3600) / 60);
                const seconds = uptimeSeconds % 60;
                
                let uptimeText = '';
                if (hours > 0) uptimeText += `${hours}h `;
                if (minutes > 0) uptimeText += `${minutes}m `;
                uptimeText += `${seconds}s`;
                
                document.getElementById('uptime').textContent = uptimeText;
                
            } catch (error) {
                console.error('Failed to refresh status:', error);
                document.getElementById('serverStatus').textContent = 'Error';
            }
        }

        // Auto-refresh every 5 seconds
        setInterval(refreshStatus, 5000);
        
        // Initial load
        refreshStatus();
    </script>
</body>
</html>

/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CHLORECOMPOSEOPSPASS
#define GEN_PASS_DECL_STABLEHLOCANONICALIZEDYNAMISMPASS
#define GEN_PASS_DECL_STABLEHLOFLATTENENTRYFUNCTIONTUPLESPASS
#define GEN_PASS_DECL_STABLEHLOFLATTENTUPLEPASS
#define GEN_PASS_DECL_STABLEHLOPREPAREFORHLOEXPORTPASS
#define GEN_PASS_DECL_STABLEHLOREFINESHAPESPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ChloRecomposeOpsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CHLORECOMPOSEOPSPASS
std::unique_ptr<::mlir::Pass> createChloRecomposeOpsPass();
#undef GEN_PASS_DECL_CHLORECOMPOSEOPSPASS
#endif // GEN_PASS_DECL_CHLORECOMPOSEOPSPASS
#ifdef GEN_PASS_DEF_CHLORECOMPOSEOPSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createChloRecomposeOpsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ChloRecomposeOpsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ChloRecomposeOpsPassBase;

  ChloRecomposeOpsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ChloRecomposeOpsPassBase(const ChloRecomposeOpsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ChloRecomposeOpsPassBase& operator=(const ChloRecomposeOpsPassBase &) = delete;
  ChloRecomposeOpsPassBase(ChloRecomposeOpsPassBase &&) = delete;
  ChloRecomposeOpsPassBase& operator=(ChloRecomposeOpsPassBase &&) = delete;
  ~ChloRecomposeOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-chlo-recompose-ops");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-chlo-recompose-ops"; }

  ::llvm::StringRef getDescription() const override { return "(Experimental) Recompose CHLO ops serialized as custom calls."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ChloRecomposeOpsPass");
  }
  ::llvm::StringRef getName() const override { return "ChloRecomposeOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<chlo::ChloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ChloRecomposeOpsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createChloRecomposeOpsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createChloRecomposeOpsPass() {
  return impl::createChloRecomposeOpsPass();
}
#undef GEN_PASS_DEF_CHLORECOMPOSEOPSPASS
#endif // GEN_PASS_DEF_CHLORECOMPOSEOPSPASS

//===----------------------------------------------------------------------===//
// StablehloCanonicalizeDynamismPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOCANONICALIZEDYNAMISMPASS
std::unique_ptr<::mlir::Pass> createStablehloCanonicalizeDynamismPass();
#undef GEN_PASS_DECL_STABLEHLOCANONICALIZEDYNAMISMPASS
#endif // GEN_PASS_DECL_STABLEHLOCANONICALIZEDYNAMISMPASS
#ifdef GEN_PASS_DEF_STABLEHLOCANONICALIZEDYNAMISMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloCanonicalizeDynamismPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloCanonicalizeDynamismPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloCanonicalizeDynamismPassBase;

  StablehloCanonicalizeDynamismPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloCanonicalizeDynamismPassBase(const StablehloCanonicalizeDynamismPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloCanonicalizeDynamismPassBase& operator=(const StablehloCanonicalizeDynamismPassBase &) = delete;
  StablehloCanonicalizeDynamismPassBase(StablehloCanonicalizeDynamismPassBase &&) = delete;
  StablehloCanonicalizeDynamismPassBase& operator=(StablehloCanonicalizeDynamismPassBase &&) = delete;
  ~StablehloCanonicalizeDynamismPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-canonicalize-dynamism");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-canonicalize-dynamism"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalizes dynamic StableHLO ops into static ops, with some XLA specialization."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloCanonicalizeDynamismPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloCanonicalizeDynamismPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::chlo::ChloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloCanonicalizeDynamismPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloCanonicalizeDynamismPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloCanonicalizeDynamismPass() {
  return impl::createStablehloCanonicalizeDynamismPass();
}
#undef GEN_PASS_DEF_STABLEHLOCANONICALIZEDYNAMISMPASS
#endif // GEN_PASS_DEF_STABLEHLOCANONICALIZEDYNAMISMPASS

//===----------------------------------------------------------------------===//
// StablehloFlattenEntryFunctionTuplesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOFLATTENENTRYFUNCTIONTUPLESPASS
struct StablehloFlattenEntryFunctionTuplesPassOptions {
  std::string entryFunctionNameOption;
};
std::unique_ptr<::mlir::Pass> createStablehloFlattenEntryFunctionTuplesPass();
std::unique_ptr<::mlir::Pass> createStablehloFlattenEntryFunctionTuplesPass(StablehloFlattenEntryFunctionTuplesPassOptions options);
#undef GEN_PASS_DECL_STABLEHLOFLATTENENTRYFUNCTIONTUPLESPASS
#endif // GEN_PASS_DECL_STABLEHLOFLATTENENTRYFUNCTIONTUPLESPASS
#ifdef GEN_PASS_DEF_STABLEHLOFLATTENENTRYFUNCTIONTUPLESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloFlattenEntryFunctionTuplesPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloFlattenEntryFunctionTuplesPass(StablehloFlattenEntryFunctionTuplesPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloFlattenEntryFunctionTuplesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloFlattenEntryFunctionTuplesPassBase;

  StablehloFlattenEntryFunctionTuplesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloFlattenEntryFunctionTuplesPassBase(const StablehloFlattenEntryFunctionTuplesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloFlattenEntryFunctionTuplesPassBase& operator=(const StablehloFlattenEntryFunctionTuplesPassBase &) = delete;
  StablehloFlattenEntryFunctionTuplesPassBase(StablehloFlattenEntryFunctionTuplesPassBase &&) = delete;
  StablehloFlattenEntryFunctionTuplesPassBase& operator=(StablehloFlattenEntryFunctionTuplesPassBase &&) = delete;
  ~StablehloFlattenEntryFunctionTuplesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-expand-flatten-entry-function-tuples");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-expand-flatten-entry-function-tuples"; }

  ::llvm::StringRef getDescription() const override { return "Flatten HLO tuple for the entry function of the module."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloFlattenEntryFunctionTuplesPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloFlattenEntryFunctionTuplesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloFlattenEntryFunctionTuplesPassBase<DerivedT>)

  StablehloFlattenEntryFunctionTuplesPassBase(StablehloFlattenEntryFunctionTuplesPassOptions options) : StablehloFlattenEntryFunctionTuplesPassBase() {
    entryFunctionNameOption = std::move(options.entryFunctionNameOption);
  }
protected:
  ::mlir::Pass::Option<std::string> entryFunctionNameOption{*this, "entry-function", ::llvm::cl::desc("the name of entry function of the module")};
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloFlattenEntryFunctionTuplesPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createStablehloFlattenEntryFunctionTuplesPass(StablehloFlattenEntryFunctionTuplesPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloFlattenEntryFunctionTuplesPass() {
  return impl::createStablehloFlattenEntryFunctionTuplesPass();
}

std::unique_ptr<::mlir::Pass> createStablehloFlattenEntryFunctionTuplesPass(StablehloFlattenEntryFunctionTuplesPassOptions options) {
  return impl::createStablehloFlattenEntryFunctionTuplesPass(std::move(options));
}
#undef GEN_PASS_DEF_STABLEHLOFLATTENENTRYFUNCTIONTUPLESPASS
#endif // GEN_PASS_DEF_STABLEHLOFLATTENENTRYFUNCTIONTUPLESPASS

//===----------------------------------------------------------------------===//
// StablehloFlattenTuplePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOFLATTENTUPLEPASS
#undef GEN_PASS_DECL_STABLEHLOFLATTENTUPLEPASS
#endif // GEN_PASS_DECL_STABLEHLOFLATTENTUPLEPASS
#ifdef GEN_PASS_DEF_STABLEHLOFLATTENTUPLEPASS
namespace impl {

template <typename DerivedT>
class StablehloFlattenTuplePassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloFlattenTuplePassBase;

  StablehloFlattenTuplePassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloFlattenTuplePassBase(const StablehloFlattenTuplePassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloFlattenTuplePassBase& operator=(const StablehloFlattenTuplePassBase &) = delete;
  StablehloFlattenTuplePassBase(StablehloFlattenTuplePassBase &&) = delete;
  StablehloFlattenTuplePassBase& operator=(StablehloFlattenTuplePassBase &&) = delete;
  ~StablehloFlattenTuplePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-flatten-tuple");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-flatten-tuple"; }

  ::llvm::StringRef getDescription() const override { return "Flatten tuples in operands and results of operators that support both tuple and variadic type."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloFlattenTuplePass");
  }
  ::llvm::StringRef getName() const override { return "StablehloFlattenTuplePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloFlattenTuplePassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_STABLEHLOFLATTENTUPLEPASS
#endif // GEN_PASS_DEF_STABLEHLOFLATTENTUPLEPASS

//===----------------------------------------------------------------------===//
// StablehloPrepareForHloExportPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOPREPAREFORHLOEXPORTPASS
std::unique_ptr<::mlir::Pass> createStablehloPrepareForHloExportPass();
#undef GEN_PASS_DECL_STABLEHLOPREPAREFORHLOEXPORTPASS
#endif // GEN_PASS_DECL_STABLEHLOPREPAREFORHLOEXPORTPASS
#ifdef GEN_PASS_DEF_STABLEHLOPREPAREFORHLOEXPORTPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloPrepareForHloExportPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloPrepareForHloExportPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloPrepareForHloExportPassBase;

  StablehloPrepareForHloExportPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloPrepareForHloExportPassBase(const StablehloPrepareForHloExportPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloPrepareForHloExportPassBase& operator=(const StablehloPrepareForHloExportPassBase &) = delete;
  StablehloPrepareForHloExportPassBase(StablehloPrepareForHloExportPassBase &&) = delete;
  StablehloPrepareForHloExportPassBase& operator=(StablehloPrepareForHloExportPassBase &&) = delete;
  ~StablehloPrepareForHloExportPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-prepare-for-hlo-export");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-prepare-for-hlo-export"; }

  ::llvm::StringRef getDescription() const override { return "Prepare StableHLO for HLO export"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloPrepareForHloExportPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloPrepareForHloExportPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloPrepareForHloExportPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloPrepareForHloExportPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloPrepareForHloExportPass() {
  return impl::createStablehloPrepareForHloExportPass();
}
#undef GEN_PASS_DEF_STABLEHLOPREPAREFORHLOEXPORTPASS
#endif // GEN_PASS_DEF_STABLEHLOPREPAREFORHLOEXPORTPASS

//===----------------------------------------------------------------------===//
// StablehloRefineShapesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOREFINESHAPESPASS
std::unique_ptr<::mlir::Pass> createStablehloRefineShapesPass();
#undef GEN_PASS_DECL_STABLEHLOREFINESHAPESPASS
#endif // GEN_PASS_DECL_STABLEHLOREFINESHAPESPASS
#ifdef GEN_PASS_DEF_STABLEHLOREFINESHAPESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloRefineShapesPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloRefineShapesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloRefineShapesPassBase;

  StablehloRefineShapesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloRefineShapesPassBase(const StablehloRefineShapesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloRefineShapesPassBase& operator=(const StablehloRefineShapesPassBase &) = delete;
  StablehloRefineShapesPassBase(StablehloRefineShapesPassBase &&) = delete;
  StablehloRefineShapesPassBase& operator=(StablehloRefineShapesPassBase &&) = delete;
  ~StablehloRefineShapesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-refine-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-refine-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Refines shapes across a StableHLO program, with some XLA specialization."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloRefineShapesPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloRefineShapesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloRefineShapesPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloRefineShapesPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloRefineShapesPass() {
  return impl::createStablehloRefineShapesPass();
}
#undef GEN_PASS_DEF_STABLEHLOREFINESHAPESPASS
#endif // GEN_PASS_DEF_STABLEHLOREFINESHAPESPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ChloRecomposeOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerChloRecomposeOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createChloRecomposeOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerChloRecomposeOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createChloRecomposeOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloCanonicalizeDynamismPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloCanonicalizeDynamismPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloCanonicalizeDynamismPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloCanonicalizeDynamismPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloCanonicalizeDynamismPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloFlattenEntryFunctionTuplesPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloFlattenEntryFunctionTuplesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloFlattenEntryFunctionTuplesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloFlattenEntryFunctionTuplesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloFlattenEntryFunctionTuplesPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloFlattenTuplePass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloFlattenTuplePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloFlattenTuplePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloFlattenTuplePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloFlattenTuplePass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloPrepareForHloExportPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloPrepareForHloExportPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloPrepareForHloExportPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloPrepareForHloExportPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloPrepareForHloExportPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloRefineShapesPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloRefineShapesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloRefineShapesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloRefineShapesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloRefineShapesPass();
  });
}

//===----------------------------------------------------------------------===//
//  Registration
//===----------------------------------------------------------------------===//

inline void registerPasses() {
  registerChloRecomposeOpsPass();
  registerStablehloCanonicalizeDynamismPass();
  registerStablehloFlattenEntryFunctionTuplesPass();
  registerStablehloFlattenTuplePass();
  registerStablehloPrepareForHloExportPass();
  registerStablehloRefineShapesPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ChloRecomposeOpsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ChloRecomposeOpsPassBase;

  ChloRecomposeOpsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ChloRecomposeOpsPassBase(const ChloRecomposeOpsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ChloRecomposeOpsPassBase& operator=(const ChloRecomposeOpsPassBase &) = delete;
  ChloRecomposeOpsPassBase(ChloRecomposeOpsPassBase &&) = delete;
  ChloRecomposeOpsPassBase& operator=(ChloRecomposeOpsPassBase &&) = delete;
  ~ChloRecomposeOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-chlo-recompose-ops");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-chlo-recompose-ops"; }

  ::llvm::StringRef getDescription() const override { return "(Experimental) Recompose CHLO ops serialized as custom calls."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ChloRecomposeOpsPass");
  }
  ::llvm::StringRef getName() const override { return "ChloRecomposeOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<chlo::ChloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ChloRecomposeOpsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloCanonicalizeDynamismPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloCanonicalizeDynamismPassBase;

  StablehloCanonicalizeDynamismPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloCanonicalizeDynamismPassBase(const StablehloCanonicalizeDynamismPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloCanonicalizeDynamismPassBase& operator=(const StablehloCanonicalizeDynamismPassBase &) = delete;
  StablehloCanonicalizeDynamismPassBase(StablehloCanonicalizeDynamismPassBase &&) = delete;
  StablehloCanonicalizeDynamismPassBase& operator=(StablehloCanonicalizeDynamismPassBase &&) = delete;
  ~StablehloCanonicalizeDynamismPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-canonicalize-dynamism");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-canonicalize-dynamism"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalizes dynamic StableHLO ops into static ops, with some XLA specialization."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloCanonicalizeDynamismPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloCanonicalizeDynamismPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::chlo::ChloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloCanonicalizeDynamismPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloFlattenEntryFunctionTuplesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloFlattenEntryFunctionTuplesPassBase;

  StablehloFlattenEntryFunctionTuplesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloFlattenEntryFunctionTuplesPassBase(const StablehloFlattenEntryFunctionTuplesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloFlattenEntryFunctionTuplesPassBase& operator=(const StablehloFlattenEntryFunctionTuplesPassBase &) = delete;
  StablehloFlattenEntryFunctionTuplesPassBase(StablehloFlattenEntryFunctionTuplesPassBase &&) = delete;
  StablehloFlattenEntryFunctionTuplesPassBase& operator=(StablehloFlattenEntryFunctionTuplesPassBase &&) = delete;
  ~StablehloFlattenEntryFunctionTuplesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-expand-flatten-entry-function-tuples");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-expand-flatten-entry-function-tuples"; }

  ::llvm::StringRef getDescription() const override { return "Flatten HLO tuple for the entry function of the module."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloFlattenEntryFunctionTuplesPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloFlattenEntryFunctionTuplesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloFlattenEntryFunctionTuplesPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> entryFunctionNameOption{*this, "entry-function", ::llvm::cl::desc("the name of entry function of the module")};
};

template <typename DerivedT>
class StablehloFlattenTuplePassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloFlattenTuplePassBase;

  StablehloFlattenTuplePassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloFlattenTuplePassBase(const StablehloFlattenTuplePassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloFlattenTuplePassBase& operator=(const StablehloFlattenTuplePassBase &) = delete;
  StablehloFlattenTuplePassBase(StablehloFlattenTuplePassBase &&) = delete;
  StablehloFlattenTuplePassBase& operator=(StablehloFlattenTuplePassBase &&) = delete;
  ~StablehloFlattenTuplePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-flatten-tuple");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-flatten-tuple"; }

  ::llvm::StringRef getDescription() const override { return "Flatten tuples in operands and results of operators that support both tuple and variadic type."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloFlattenTuplePass");
  }
  ::llvm::StringRef getName() const override { return "StablehloFlattenTuplePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloFlattenTuplePassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloPrepareForHloExportPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloPrepareForHloExportPassBase;

  StablehloPrepareForHloExportPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloPrepareForHloExportPassBase(const StablehloPrepareForHloExportPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloPrepareForHloExportPassBase& operator=(const StablehloPrepareForHloExportPassBase &) = delete;
  StablehloPrepareForHloExportPassBase(StablehloPrepareForHloExportPassBase &&) = delete;
  StablehloPrepareForHloExportPassBase& operator=(StablehloPrepareForHloExportPassBase &&) = delete;
  ~StablehloPrepareForHloExportPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-prepare-for-hlo-export");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-prepare-for-hlo-export"; }

  ::llvm::StringRef getDescription() const override { return "Prepare StableHLO for HLO export"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloPrepareForHloExportPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloPrepareForHloExportPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloPrepareForHloExportPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloRefineShapesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloRefineShapesPassBase;

  StablehloRefineShapesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloRefineShapesPassBase(const StablehloRefineShapesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloRefineShapesPassBase& operator=(const StablehloRefineShapesPassBase &) = delete;
  StablehloRefineShapesPassBase(StablehloRefineShapesPassBase &&) = delete;
  StablehloRefineShapesPassBase& operator=(StablehloRefineShapesPassBase &&) = delete;
  ~StablehloRefineShapesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-ext-refine-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-ext-refine-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Refines shapes across a StableHLO program, with some XLA specialization."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloRefineShapesPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloRefineShapesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloRefineShapesPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES

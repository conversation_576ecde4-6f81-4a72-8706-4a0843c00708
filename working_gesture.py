#!/usr/bin/env python3
"""
Simple Working Hand Gesture Recognition
Uses OpenCV and basic computer vision - no complex dependencies
"""

import cv2
import numpy as np
from flask import Flask, render_template_string
from flask_socketio import Socket<PERSON>, emit
import base64
import threading
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'simple_gesture_key'
socketio = SocketIO(app, cors_allowed_origins="*")

class SimpleGestureRecognizer:
    def __init__(self):
        self.gestures = ['Stop', '1', '2', '3', 'Hello', 'Good', 'Peace', 'OK']
        
    def detect_gesture(self, frame):
        """Simple gesture detection based on contours and hand shape"""
        try:
            # Convert to HSV for better skin detection
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # Define skin color range
            lower_skin = np.array([0, 20, 70], dtype=np.uint8)
            upper_skin = np.array([20, 255, 255], dtype=np.uint8)
            
            # Create mask for skin color
            mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            # Apply morphological operations
            kernel = np.ones((3,3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # Get largest contour (hand)
                largest_contour = max(contours, key=cv2.contourArea)
                area = cv2.contourArea(largest_contour)
                
                if area > 5000:  # Minimum hand size
                    # Get convex hull
                    hull = cv2.convexHull(largest_contour, returnPoints=False)
                    defects = cv2.convexityDefects(largest_contour, hull)
                    
                    if defects is not None:
                        # Count fingers based on convexity defects
                        finger_count = 0
                        for i in range(defects.shape[0]):
                            s, e, f, d = defects[i, 0]
                            start = tuple(largest_contour[s][0])
                            end = tuple(largest_contour[e][0])
                            far = tuple(largest_contour[f][0])
                            
                            # Calculate angle
                            a = np.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
                            b = np.sqrt((far[0] - start[0])**2 + (far[1] - start[1])**2)
                            c = np.sqrt((end[0] - far[0])**2 + (end[1] - far[1])**2)
                            angle = np.arccos((b**2 + c**2 - a**2) / (2*b*c))
                            
                            if angle <= np.pi/2:  # 90 degrees
                                finger_count += 1
                        
                        # Classify gesture based on finger count
                        if finger_count == 0:
                            return 'Stop', 0.8
                        elif finger_count == 1:
                            return '1', 0.8
                        elif finger_count == 2:
                            return '2', 0.8
                        elif finger_count == 3:
                            return '3', 0.8
                        elif finger_count == 4:
                            return 'Hello', 0.8
                        else:
                            return 'Hello', 0.7
            
            return None, 0.0
            
        except Exception as e:
            print(f"Gesture detection error: {e}")
            return None, 0.0

gesture_recognizer = SimpleGestureRecognizer()

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Working Gesture Recognition</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        #video { width: 100%; max-width: 640px; border: 2px solid #ddd; border-radius: 10px; display: block; margin: 0 auto; }
        .controls { text-align: center; margin: 20px 0; }
        button { background: #667eea; color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; margin: 10px; font-size: 16px; transition: all 0.3s ease; }
        button:hover { background: #5a6fd8; transform: translateY(-2px); }
        button:disabled { opacity: 0.5; cursor: not-allowed; transform: none; }
        button.active { background: #27ae60; }
        .status { text-align: center; margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #667eea; }
        .gesture-display { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 10px; padding: 20px; margin: 20px 0; text-align: center; display: none; }
        .gesture-text { font-size: 32px; font-weight: bold; color: #155724; margin-bottom: 10px; }
        .confidence { font-size: 16px; color: #666; }
        .instructions { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; padding: 20px; margin: 20px 0; }
        .instructions h3 { margin-top: 0; color: #856404; }
        .instructions ul { margin: 10px 0; padding-left: 20px; }
        .instructions li { margin: 5px 0; color: #856404; }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <h1>🤲 Working Hand Gesture Recognition</h1>
        
        <video id="video" autoplay muted playsinline></video>
        
        <div class="controls">
            <button onclick="startCamera()">Start Camera</button>
            <button onclick="startGestureRecognition()" id="gestureBtn" disabled>Start Gesture Recognition</button>
            <button onclick="stopAll()">Stop</button>
        </div>
        
        <div class="status" id="status">Click "Start Camera" to begin</div>
        
        <div class="gesture-display" id="gestureDisplay">
            <div class="gesture-text" id="gestureText">No gesture detected</div>
            <div class="confidence" id="confidence">Confidence: 0%</div>
        </div>
        
        <div class="instructions">
            <h3>📋 How to Use:</h3>
            <ul>
                <li><strong>Step 1:</strong> Click "Start Camera" and allow camera access</li>
                <li><strong>Step 2:</strong> Click "Start Gesture Recognition"</li>
                <li><strong>Step 3:</strong> Show your hand clearly to the camera</li>
                <li><strong>Step 4:</strong> Make gestures with good lighting</li>
            </ul>
            
            <h3>👋 Supported Gestures:</h3>
            <ul>
                <li>✊ <strong>Closed Fist</strong> → "Stop"</li>
                <li>☝️ <strong>One Finger</strong> → "1"</li>
                <li>✌️ <strong>Two Fingers</strong> → "2"</li>
                <li>🤟 <strong>Three Fingers</strong> → "3"</li>
                <li>🖐️ <strong>Open Hand</strong> → "Hello"</li>
            </ul>
        </div>
    </div>

    <script>
        const socket = io();
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        const gestureDisplay = document.getElementById('gestureDisplay');
        const gestureText = document.getElementById('gestureText');
        const confidence = document.getElementById('confidence');
        const gestureBtn = document.getElementById('gestureBtn');
        
        let stream = null;
        let isRecognizing = false;
        
        async function startCamera() {
            try {
                status.textContent = 'Starting camera...';
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { width: 640, height: 480 } 
                });
                video.srcObject = stream;
                
                video.onloadedmetadata = () => {
                    status.textContent = 'Camera started! Click "Start Gesture Recognition"';
                    gestureBtn.disabled = false;
                };
            } catch (error) {
                status.textContent = 'Error: Could not access camera - ' + error.message;
            }
        }
        
        function startGestureRecognition() {
            if (!stream) {
                status.textContent = 'Please start camera first!';
                return;
            }
            
            isRecognizing = true;
            gestureDisplay.style.display = 'block';
            status.textContent = 'Gesture recognition active - Show your hand!';
            gestureBtn.textContent = 'Stop Gesture Recognition';
            gestureBtn.classList.add('active');
            gestureBtn.onclick = stopGestureRecognition;
            
            sendFrames();
        }
        
        function stopGestureRecognition() {
            isRecognizing = false;
            gestureDisplay.style.display = 'none';
            status.textContent = 'Gesture recognition stopped';
            gestureBtn.textContent = 'Start Gesture Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.onclick = startGestureRecognition;
        }
        
        function sendFrames() {
            if (!isRecognizing) return;
            
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            ctx.drawImage(video, 0, 0);
            const imageData = canvas.toDataURL('image/jpeg', 0.8);
            
            socket.emit('process_frame', { image: imageData });
            
            setTimeout(sendFrames, 500); // Send frame every 500ms
        }
        
        function stopAll() {
            isRecognizing = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            gestureDisplay.style.display = 'none';
            status.textContent = 'Stopped. Click "Start Camera" to begin again.';
            gestureBtn.disabled = true;
            gestureBtn.textContent = 'Start Gesture Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.onclick = startGestureRecognition;
        }
        
        socket.on('gesture_result', function(data) {
            if (data.gesture) {
                gestureText.textContent = data.gesture;
                confidence.textContent = `Confidence: ${Math.round(data.confidence * 100)}%`;
                console.log('Gesture detected:', data.gesture);
            } else {
                gestureText.textContent = 'No hand detected';
                confidence.textContent = 'Show your hand to the camera';
            }
        });
        
        socket.on('connect', function() {
            console.log('Connected to gesture recognition server!');
        });
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@socketio.on('process_frame')
def process_frame(data):
    """Process video frame and detect gestures"""
    try:
        # Decode base64 image
        image_data = data['image'].split(',')[1]
        image_bytes = base64.b64decode(image_data)
        
        # Convert to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if frame is None:
            emit('gesture_result', {'gesture': None, 'confidence': 0})
            return
        
        # Detect gesture
        gesture, confidence = gesture_recognizer.detect_gesture(frame)
        
        emit('gesture_result', {
            'gesture': gesture,
            'confidence': confidence
        })
        
    except Exception as e:
        print(f"Error processing frame: {e}")
        emit('gesture_result', {'gesture': None, 'confidence': 0})

if __name__ == '__main__':
    print("🚀 Starting Simple Gesture Recognition Server...")
    print("📦 Only requires: pip install flask flask-socketio opencv-python")
    print("🌐 Open: http://localhost:3000")
    print("✅ No MediaPipe or complex dependencies needed!")
    socketio.run(app, host='0.0.0.0', port=3000, debug=True)

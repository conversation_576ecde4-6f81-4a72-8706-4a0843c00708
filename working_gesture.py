#!/usr/bin/env python3
"""
Simple Working Hand Gesture Recognition
Uses OpenCV and basic computer vision - no complex dependencies
"""

import cv2
import numpy as np
from flask import Flask, render_template_string
from flask_socketio import Socket<PERSON>, emit
import base64
import threading
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'simple_gesture_key'
socketio = SocketIO(app, cors_allowed_origins="*")

class SimpleGestureRecognizer:
    def __init__(self):
        self.gestures = ['Stop', '1', '2', '3', 'Hello', 'Good', 'Peace', 'OK']
        self.gesture_history = []
        self.session_stats = {
            'total_gestures': 0,
            'successful_detections': 0,
            'session_start': time.time(),
            'gesture_counts': {},
            'confidence_scores': []
        }
        self.calibration_data = {
            'skin_ranges': [],
            'hand_size_range': [2000, 50000],
            'is_calibrated': False
        }

    def detect_gesture(self, frame):
        """Enhanced gesture detection with multiple approaches"""
        try:
            height, width = frame.shape[:2]

            # Convert to different color spaces
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            ycrcb = cv2.cvtColor(frame, cv2.COLOR_BGR2YCrCb)

            # Multiple skin detection approaches
            # HSV skin detection
            lower_hsv = np.array([0, 30, 60], dtype=np.uint8)
            upper_hsv = np.array([20, 150, 255], dtype=np.uint8)
            mask_hsv = cv2.inRange(hsv, lower_hsv, upper_hsv)

            # YCrCb skin detection (often more reliable)
            lower_ycrcb = np.array([0, 135, 85], dtype=np.uint8)
            upper_ycrcb = np.array([255, 180, 135], dtype=np.uint8)
            mask_ycrcb = cv2.inRange(ycrcb, lower_ycrcb, upper_ycrcb)

            # Combine masks
            mask = cv2.bitwise_or(mask_hsv, mask_ycrcb)

            # Noise reduction
            mask = cv2.medianBlur(mask, 5)
            mask = cv2.GaussianBlur(mask, (5, 5), 0)

            # Morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=2)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)

            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                # Filter contours by area and position
                valid_contours = []
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 2000:  # Minimum hand size
                        # Check if contour is in reasonable position (not edges)
                        x, y, w, h = cv2.boundingRect(contour)
                        if (x > width * 0.1 and x + w < width * 0.9 and
                            y > height * 0.1 and y + h < height * 0.9):
                            valid_contours.append(contour)

                if valid_contours:
                    # Get largest valid contour
                    largest_contour = max(valid_contours, key=cv2.contourArea)
                    area = cv2.contourArea(largest_contour)

                    print(f"Hand area: {area}")  # Debug

                    # Enhanced gesture classification
                    gesture, confidence = self.classify_hand_shape(largest_contour, area)
                    if gesture:
                        return gesture, confidence

            return None, 0.0

        except Exception as e:
            print(f"Gesture detection error: {e}")
            return None, 0.0

    def classify_hand_shape(self, contour, area):
        """Enhanced gesture classification"""
        try:
            # Get convex hull and defects
            hull = cv2.convexHull(contour, returnPoints=False)
            if len(hull) > 3:
                defects = cv2.convexityDefects(contour, hull)

                if defects is not None:
                    # Count significant defects (fingers)
                    finger_count = 0
                    for i in range(defects.shape[0]):
                        s, e, f, d = defects[i, 0]
                        start = tuple(contour[s][0])
                        end = tuple(contour[e][0])
                        far = tuple(contour[f][0])

                        # Calculate angle between vectors
                        a = np.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
                        b = np.sqrt((far[0] - start[0])**2 + (far[1] - start[1])**2)
                        c = np.sqrt((end[0] - far[0])**2 + (end[1] - far[1])**2)

                        if b > 0 and c > 0:
                            angle = np.arccos((b**2 + c**2 - a**2) / (2*b*c))

                            # Count as finger if angle is acute and defect is deep enough
                            if angle <= np.pi/2 and d > 1000:
                                finger_count += 1

                    print(f"Finger count: {finger_count}, Area: {area}")  # Debug

                    # Classify based on finger count and area
                    if finger_count == 0:
                        if area < 6000:
                            return '1', 0.85  # Small area, likely one finger
                        else:
                            return 'Stop', 0.9  # Large solid area, likely fist
                    elif finger_count == 1:
                        return '2', 0.85  # One defect = two fingers
                    elif finger_count == 2:
                        return '3', 0.8   # Two defects = three fingers
                    elif finger_count >= 3:
                        return 'Hello', 0.9  # Multiple defects = open hand

            # Fallback: classify by area and solidity
            hull_points = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull_points)

            if hull_area > 0:
                solidity = area / hull_area

                if solidity > 0.85:
                    return 'Stop', 0.7  # Very solid = fist
                elif solidity < 0.6:
                    return 'Hello', 0.7  # Not solid = open hand
                else:
                    return '1', 0.6  # Medium solidity = partial hand

            return 'Hello', 0.5  # Default fallback

        except Exception as e:
            print(f"Classification error: {e}")
            return None, 0.0

    def update_stats(self, gesture, confidence):
        """Update session statistics"""
        self.session_stats['total_gestures'] += 1
        if confidence > 0.7:
            self.session_stats['successful_detections'] += 1

        if gesture in self.session_stats['gesture_counts']:
            self.session_stats['gesture_counts'][gesture] += 1
        else:
            self.session_stats['gesture_counts'][gesture] = 1

        self.session_stats['confidence_scores'].append(confidence)

        # Keep history of last 10 gestures
        self.gesture_history.append({
            'gesture': gesture,
            'confidence': confidence,
            'timestamp': time.time()
        })
        if len(self.gesture_history) > 10:
            self.gesture_history.pop(0)

    def get_session_stats(self):
        """Get comprehensive session statistics"""
        total_time = time.time() - self.session_stats['session_start']
        avg_confidence = sum(self.session_stats['confidence_scores']) / len(self.session_stats['confidence_scores']) if self.session_stats['confidence_scores'] else 0

        return {
            'total_gestures': self.session_stats['total_gestures'],
            'successful_detections': self.session_stats['successful_detections'],
            'accuracy': (self.session_stats['successful_detections'] / max(1, self.session_stats['total_gestures'])) * 100,
            'session_duration': total_time,
            'gestures_per_minute': (self.session_stats['total_gestures'] / max(1, total_time / 60)),
            'average_confidence': avg_confidence,
            'gesture_counts': self.session_stats['gesture_counts'],
            'recent_history': self.gesture_history[-5:]  # Last 5 gestures
        }

    def calibrate_skin_detection(self, frame, hand_region):
        """Calibrate skin detection based on user's hand"""
        try:
            x, y, w, h = hand_region
            hand_sample = frame[y:y+h, x:x+w]

            # Convert to HSV and YCrCb
            hsv_sample = cv2.cvtColor(hand_sample, cv2.COLOR_BGR2HSV)
            ycrcb_sample = cv2.cvtColor(hand_sample, cv2.COLOR_BGR2YCrCb)

            # Calculate skin color ranges
            hsv_mean = np.mean(hsv_sample.reshape(-1, 3), axis=0)
            hsv_std = np.std(hsv_sample.reshape(-1, 3), axis=0)

            ycrcb_mean = np.mean(ycrcb_sample.reshape(-1, 3), axis=0)
            ycrcb_std = np.std(ycrcb_sample.reshape(-1, 3), axis=0)

            # Store calibration data
            self.calibration_data['skin_ranges'] = {
                'hsv_lower': hsv_mean - 2 * hsv_std,
                'hsv_upper': hsv_mean + 2 * hsv_std,
                'ycrcb_lower': ycrcb_mean - 2 * ycrcb_std,
                'ycrcb_upper': ycrcb_mean + 2 * ycrcb_std
            }
            self.calibration_data['is_calibrated'] = True

            return True
        except Exception as e:
            print(f"Calibration error: {e}")
            return False

gesture_recognizer = SimpleGestureRecognizer()

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>🤲 Professional Hand Gesture Recognition</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
            margin-bottom: 30px;
        }

        .video-section {
            background: #000;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            aspect-ratio: 16/9;
            max-height: 450px;
        }

        #video {
            width: 100%;
            height: 100%;
            display: block;
            object-fit: cover;
            border-radius: 20px;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            transition: opacity 0.3s ease;
        }

        .video-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .controls-panel {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 25px;
            height: fit-content;
        }

        .controls-panel h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }

        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        button.active {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        button.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 15px;
            border-left: 5px solid #667eea;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: 500;
        }

        .status.success {
            border-left-color: #27ae60;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .status.error {
            border-left-color: #e74c3c;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .gesture-display {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
            display: none;
            box-shadow: 0 10px 30px rgba(39, 174, 96, 0.1);
            border: 2px solid rgba(39, 174, 96, 0.2);
        }

        .gesture-text {
            font-size: 3rem;
            font-weight: bold;
            color: #155724;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .confidence {
            font-size: 1.2rem;
            color: #666;
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .instructions {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 20px;
            padding: 25px;
            margin: 30px 0;
            border: 2px solid rgba(255, 193, 7, 0.3);
        }

        .instructions h3 {
            margin-bottom: 15px;
            color: #856404;
            font-size: 1.3rem;
        }

        .instructions ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .instructions li {
            margin: 8px 0;
            color: #856404;
            line-height: 1.5;
        }

        .gesture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }

        .gesture-item {
            background: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .gesture-item:hover {
            transform: translateY(-3px);
        }

        .gesture-emoji {
            font-size: 2rem;
            margin-bottom: 8px;
        }

        .gesture-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .gesture-desc {
            font-size: 0.8rem;
            color: #666;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .video-section {
                max-height: 300px;
            }

            .controls-panel {
                margin-top: 20px;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* New Interactive Elements Styles */
        .hidden {
            display: none !important;
        }

        .settings-panel {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid #667eea;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 15px;
        }

        .close-btn {
            background: #e74c3c !important;
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            padding: 0 !important;
            font-size: 18px !important;
            margin: 0 !important;
        }

        .setting-group {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .setting-group label {
            min-width: 150px;
            font-weight: 600;
            color: #333;
        }

        .setting-group input[type="range"] {
            flex: 1;
            height: 8px;
            border-radius: 4px;
            background: #ddd;
            outline: none;
        }

        .setting-group select {
            flex: 1;
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
        }

        .setting-group input[type="checkbox"] {
            width: 20px;
            height: 20px;
        }

        .history-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            border: 2px solid #e9ecef;
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-gesture {
            font-weight: 600;
            color: #667eea;
        }

        .history-confidence {
            font-size: 0.9rem;
            color: #666;
        }

        .training-panel, .analytics-panel {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .training-header, .analytics-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        .training-gestures {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .gesture-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .gesture-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .gesture-card.trained {
            border-color: #27ae60;
            background: #d4edda;
        }

        .gesture-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .gesture-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .gesture-status {
            font-size: 0.9rem;
            color: #666;
        }

        .gesture-status.trained {
            color: #27ae60;
            font-weight: 600;
        }

        .chart-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .analytics-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .stat-label {
            font-weight: 600;
            color: #333;
        }

        .stat-value {
            font-weight: bold;
            color: #667eea;
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: #e74c3c;
        }

        .notification.warning {
            background: #f39c12;
        }

        /* Pulse animation for active elements */
        .pulse {
            animation: pulse-glow 2s infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
            50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤲 Professional Hand Gesture Recognition</h1>
            <p>Real-time computer vision powered gesture detection using OpenCV</p>
        </div>

        <div class="main-content">
            <div class="video-section">
                <video id="video" autoplay muted playsinline></video>
                <div class="video-overlay" id="videoOverlay">
                    <div>
                        <div style="font-size: 3rem; margin-bottom: 20px;">📹</div>
                        <div>Click "Start Camera" to begin</div>
                    </div>
                </div>
            </div>

            <div class="controls-panel">
                <h3>🎮 Controls</h3>

                <div class="control-group">
                    <label>Camera Control</label>
                    <button onclick="startCamera()" id="cameraBtn">
                        📹 Start Camera
                    </button>
                </div>

                <div class="control-group">
                    <label>Gesture Recognition</label>
                    <button onclick="startGestureRecognition()" id="gestureBtn" disabled>
                        🤲 Start Recognition
                    </button>
                </div>

                <div class="control-group">
                    <label>Calibration</label>
                    <button onclick="calibrateGestures()" id="calibrateBtn">
                        🎯 Calibrate Hand
                    </button>
                </div>

                <div class="control-group">
                    <label>Settings</label>
                    <button onclick="toggleSettings()" id="settingsBtn">
                        ⚙️ Settings
                    </button>
                </div>

                <div class="control-group">
                    <label>Emergency Stop</label>
                    <button onclick="stopAll()" class="danger">
                        🛑 Stop All
                    </button>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="gestureCount">0</div>
                        <div class="stat-label">Gestures</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="accuracy">0%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="avgConfidence">0%</div>
                        <div class="stat-label">Avg Confidence</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="gesturesPerMin">0</div>
                        <div class="stat-label">Per Minute</div>
                    </div>
                </div>

                <div class="control-group">
                    <label>Gesture History</label>
                    <div class="history-panel" id="historyPanel">
                        <div class="history-placeholder">No gestures detected yet</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="status" id="status">
            🚀 Ready to start! Click "Start Camera" to begin gesture recognition
        </div>

        <div class="gesture-display" id="gestureDisplay">
            <div class="gesture-text" id="gestureText">No gesture detected</div>
            <div class="confidence" id="confidence">Show your hand to the camera</div>
        </div>

        <!-- Settings Panel -->
        <div class="settings-panel hidden" id="settingsPanel">
            <div class="settings-header">
                <h3>⚙️ Advanced Settings</h3>
                <button onclick="toggleSettings()" class="close-btn">✕</button>
            </div>
            <div class="settings-content">
                <div class="setting-group">
                    <label>Detection Sensitivity</label>
                    <input type="range" id="sensitivitySlider" min="0.3" max="0.9" step="0.1" value="0.7">
                    <span id="sensitivityValue">0.7</span>
                </div>
                <div class="setting-group">
                    <label>Frame Processing Rate</label>
                    <select id="frameRateSelect">
                        <option value="100">Very Fast (100ms)</option>
                        <option value="300" selected>Fast (300ms)</option>
                        <option value="500">Normal (500ms)</option>
                        <option value="1000">Slow (1000ms)</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>Hand Size Filter</label>
                    <input type="range" id="handSizeSlider" min="1000" max="10000" step="500" value="2000">
                    <span id="handSizeValue">2000px</span>
                </div>
                <div class="setting-group">
                    <label>Show Debug Info</label>
                    <input type="checkbox" id="debugCheckbox">
                </div>
                <div class="setting-group">
                    <label>Sound Notifications</label>
                    <input type="checkbox" id="soundCheckbox" checked>
                </div>
            </div>
        </div>

        <!-- Gesture Training Panel -->
        <div class="training-panel">
            <div class="training-header">
                <h3>🎓 Gesture Training Mode</h3>
                <button onclick="toggleTrainingMode()" id="trainingBtn">Start Training</button>
            </div>
            <div class="training-content hidden" id="trainingContent">
                <div class="training-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="trainingProgress"></div>
                    </div>
                    <span id="trainingText">Make the gesture: ✊ Stop</span>
                </div>
                <div class="training-gestures">
                    <div class="gesture-card" data-gesture="Stop">
                        <div class="gesture-icon">✊</div>
                        <div class="gesture-name">Stop</div>
                        <div class="gesture-status" id="status-Stop">Not trained</div>
                    </div>
                    <div class="gesture-card" data-gesture="Hello">
                        <div class="gesture-icon">🖐️</div>
                        <div class="gesture-name">Hello</div>
                        <div class="gesture-status" id="status-Hello">Not trained</div>
                    </div>
                    <div class="gesture-card" data-gesture="1">
                        <div class="gesture-icon">☝️</div>
                        <div class="gesture-name">One</div>
                        <div class="gesture-status" id="status-1">Not trained</div>
                    </div>
                    <div class="gesture-card" data-gesture="2">
                        <div class="gesture-icon">✌️</div>
                        <div class="gesture-name">Two</div>
                        <div class="gesture-status" id="status-2">Not trained</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Analytics -->
        <div class="analytics-panel">
            <div class="analytics-header">
                <h3>📊 Real-time Analytics</h3>
                <button onclick="exportData()" id="exportBtn">📥 Export Data</button>
            </div>
            <div class="analytics-content">
                <div class="chart-container">
                    <canvas id="gestureChart" width="400" height="200"></canvas>
                </div>
                <div class="analytics-stats">
                    <div class="stat-item">
                        <span class="stat-label">Session Duration:</span>
                        <span class="stat-value" id="sessionDuration">00:00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Most Common Gesture:</span>
                        <span class="stat-value" id="mostCommonGesture">None</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Best Accuracy:</span>
                        <span class="stat-value" id="bestAccuracy">0%</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>📋 How to Use This System</h3>
            <ul>
                <li><strong>Step 1:</strong> Click "Start Camera" and allow camera access when prompted</li>
                <li><strong>Step 2:</strong> Position yourself 1-2 feet from the camera with good lighting</li>
                <li><strong>Step 3:</strong> Click "Start Recognition" to begin gesture detection</li>
                <li><strong>Step 4:</strong> Make clear hand gestures and hold them for 2-3 seconds</li>
                <li><strong>Step 5:</strong> Watch the results appear in real-time above</li>
            </ul>

            <h3>👋 Supported Hand Gestures</h3>
            <div class="gesture-grid">
                <div class="gesture-item">
                    <div class="gesture-emoji">✊</div>
                    <div class="gesture-name">Stop</div>
                    <div class="gesture-desc">Closed fist</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">☝️</div>
                    <div class="gesture-name">One</div>
                    <div class="gesture-desc">Index finger up</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">✌️</div>
                    <div class="gesture-name">Two</div>
                    <div class="gesture-desc">Peace sign</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">🖐️</div>
                    <div class="gesture-name">Hello</div>
                    <div class="gesture-desc">Open hand</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">👍</div>
                    <div class="gesture-name">Good</div>
                    <div class="gesture-desc">Thumbs up</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">🤟</div>
                    <div class="gesture-name">Three</div>
                    <div class="gesture-desc">Three fingers</div>
                </div>
            </div>

            <h3>💡 Tips for Better Detection</h3>
            <ul>
                <li><strong>Lighting:</strong> Ensure bright, even lighting on your hand</li>
                <li><strong>Background:</strong> Use a plain, contrasting background</li>
                <li><strong>Distance:</strong> Keep your hand 1-2 feet from the camera</li>
                <li><strong>Stability:</strong> Hold gestures steady for 2-3 seconds</li>
                <li><strong>Clarity:</strong> Make distinct, clear finger positions</li>
            </ul>
        </div>
    </div>

    <script>
        const socket = io();
        const video = document.getElementById('video');
        const videoOverlay = document.getElementById('videoOverlay');
        const status = document.getElementById('status');
        const gestureDisplay = document.getElementById('gestureDisplay');
        const gestureText = document.getElementById('gestureText');
        const confidence = document.getElementById('confidence');
        const gestureBtn = document.getElementById('gestureBtn');
        const cameraBtn = document.getElementById('cameraBtn');
        const gestureCount = document.getElementById('gestureCount');
        const accuracy = document.getElementById('accuracy');
        const avgConfidence = document.getElementById('avgConfidence');
        const gesturesPerMin = document.getElementById('gesturesPerMin');
        const historyPanel = document.getElementById('historyPanel');
        const settingsPanel = document.getElementById('settingsPanel');
        const trainingContent = document.getElementById('trainingContent');
        const sessionDuration = document.getElementById('sessionDuration');
        const mostCommonGesture = document.getElementById('mostCommonGesture');
        const bestAccuracy = document.getElementById('bestAccuracy');

        let stream = null;
        let isRecognizing = false;
        let totalGestures = 0;
        let successfulGestures = 0;
        let sessionStartTime = Date.now();
        let gestureHistory = [];
        let isTrainingMode = false;
        let currentTrainingGesture = 0;
        let trainingGestures = ['Stop', 'Hello', '1', '2'];
        let settings = {
            sensitivity: 0.7,
            frameRate: 300,
            handSize: 2000,
            debug: false,
            sound: true
        };

        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateStats() {
            gestureCount.textContent = totalGestures;
            const acc = totalGestures > 0 ? Math.round((successfulGestures / totalGestures) * 100) : 0;
            accuracy.textContent = acc + '%';

            // Update additional stats
            const avgConf = gestureHistory.length > 0 ?
                Math.round(gestureHistory.reduce((sum, g) => sum + g.confidence, 0) / gestureHistory.length * 100) : 0;
            avgConfidence.textContent = avgConf + '%';

            const sessionTime = (Date.now() - sessionStartTime) / 1000 / 60; // minutes
            const gpm = sessionTime > 0 ? Math.round(totalGestures / sessionTime) : 0;
            gesturesPerMin.textContent = gpm;

            // Update session duration
            const duration = Math.floor((Date.now() - sessionStartTime) / 1000);
            const minutes = Math.floor(duration / 60);
            const seconds = duration % 60;
            sessionDuration.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            // Update most common gesture
            if (gestureHistory.length > 0) {
                const gestureCounts = {};
                gestureHistory.forEach(g => {
                    gestureCounts[g.gesture] = (gestureCounts[g.gesture] || 0) + 1;
                });
                const mostCommon = Object.keys(gestureCounts).reduce((a, b) =>
                    gestureCounts[a] > gestureCounts[b] ? a : b);
                mostCommonGesture.textContent = mostCommon;
            }

            // Update best accuracy
            const currentBest = parseInt(bestAccuracy.textContent) || 0;
            if (acc > currentBest) {
                bestAccuracy.textContent = acc + '%';
            }
        }

        function addToHistory(gesture, confidence) {
            gestureHistory.push({ gesture, confidence, timestamp: Date.now() });
            if (gestureHistory.length > 50) {
                gestureHistory.shift(); // Keep last 50 gestures
            }

            updateHistoryDisplay();
        }

        function updateHistoryDisplay() {
            const recentGestures = gestureHistory.slice(-5).reverse(); // Last 5, newest first

            if (recentGestures.length === 0) {
                historyPanel.innerHTML = '<div class="history-placeholder">No gestures detected yet</div>';
                return;
            }

            historyPanel.innerHTML = recentGestures.map(g => `
                <div class="history-item">
                    <span class="history-gesture">${g.gesture}</span>
                    <span class="history-confidence">${Math.round(g.confidence * 100)}%</span>
                </div>
            `).join('');
        }

        function toggleSettings() {
            settingsPanel.classList.toggle('hidden');

            if (!settingsPanel.classList.contains('hidden')) {
                // Initialize settings values
                document.getElementById('sensitivitySlider').value = settings.sensitivity;
                document.getElementById('sensitivityValue').textContent = settings.sensitivity;
                document.getElementById('frameRateSelect').value = settings.frameRate;
                document.getElementById('handSizeSlider').value = settings.handSize;
                document.getElementById('handSizeValue').textContent = settings.handSize + 'px';
                document.getElementById('debugCheckbox').checked = settings.debug;
                document.getElementById('soundCheckbox').checked = settings.sound;

                // Add event listeners for settings
                setupSettingsListeners();
            }
        }

        function setupSettingsListeners() {
            const sensitivitySlider = document.getElementById('sensitivitySlider');
            const frameRateSelect = document.getElementById('frameRateSelect');
            const handSizeSlider = document.getElementById('handSizeSlider');
            const debugCheckbox = document.getElementById('debugCheckbox');
            const soundCheckbox = document.getElementById('soundCheckbox');

            sensitivitySlider.addEventListener('input', (e) => {
                settings.sensitivity = parseFloat(e.target.value);
                document.getElementById('sensitivityValue').textContent = settings.sensitivity;
                showNotification(`Sensitivity updated to ${settings.sensitivity}`, 'success');
            });

            frameRateSelect.addEventListener('change', (e) => {
                settings.frameRate = parseInt(e.target.value);
                showNotification(`Frame rate updated to ${settings.frameRate}ms`, 'success');
            });

            handSizeSlider.addEventListener('input', (e) => {
                settings.handSize = parseInt(e.target.value);
                document.getElementById('handSizeValue').textContent = settings.handSize + 'px';
                showNotification(`Hand size filter updated to ${settings.handSize}px`, 'success');
            });

            debugCheckbox.addEventListener('change', (e) => {
                settings.debug = e.target.checked;
                showNotification(`Debug mode ${settings.debug ? 'enabled' : 'disabled'}`, 'success');
            });

            soundCheckbox.addEventListener('change', (e) => {
                settings.sound = e.target.checked;
                showNotification(`Sound notifications ${settings.sound ? 'enabled' : 'disabled'}`, 'success');
            });
        }

        async function startCamera() {
            try {
                updateStatus('🔄 Starting camera...', 'info');
                cameraBtn.innerHTML = '<div class="loading"></div> Starting...';
                cameraBtn.disabled = true;

                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    }
                });
                video.srcObject = stream;

                video.onloadedmetadata = () => {
                    videoOverlay.classList.add('hidden');
                    updateStatus('✅ Camera started! Click "Start Recognition" to begin', 'success');
                    cameraBtn.innerHTML = '📹 Camera Active';
                    cameraBtn.classList.add('active');
                    gestureBtn.disabled = false;
                };
            } catch (error) {
                updateStatus('❌ Camera access denied: ' + error.message, 'error');
                cameraBtn.innerHTML = '📹 Start Camera';
                cameraBtn.disabled = false;
                console.error('Camera error:', error);
            }
        }

        function startGestureRecognition() {
            if (!stream) {
                updateStatus('❌ Please start camera first!', 'error');
                return;
            }

            isRecognizing = true;
            gestureDisplay.style.display = 'block';
            updateStatus('🎯 Gesture recognition active! Show your hand clearly', 'success');
            gestureBtn.innerHTML = '🛑 Stop Recognition';
            gestureBtn.classList.add('active');
            gestureBtn.onclick = stopGestureRecognition;

            sendFrames();
        }

        function stopGestureRecognition() {
            isRecognizing = false;
            gestureDisplay.style.display = 'none';
            updateStatus('⏹️ Gesture recognition stopped', 'info');
            gestureBtn.innerHTML = '🤲 Start Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.onclick = startGestureRecognition;
        }

        function sendFrames() {
            if (!isRecognizing) return;

            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = video.videoWidth || 640;
                canvas.height = video.videoHeight || 480;

                ctx.drawImage(video, 0, 0);
                const imageData = canvas.toDataURL('image/jpeg', 0.7);

                socket.emit('process_frame', { image: imageData });

                setTimeout(sendFrames, settings.frameRate); // Use dynamic frame rate
            } catch (error) {
                console.error('Frame processing error:', error);
                setTimeout(sendFrames, 1000); // Retry after 1 second
            }
        }

        function stopAll() {
            isRecognizing = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            videoOverlay.classList.remove('hidden');
            gestureDisplay.style.display = 'none';

            updateStatus('🛑 All systems stopped. Click "Start Camera" to begin again.', 'info');

            // Reset buttons
            cameraBtn.innerHTML = '📹 Start Camera';
            cameraBtn.classList.remove('active');
            cameraBtn.disabled = false;

            gestureBtn.innerHTML = '🤲 Start Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.disabled = true;
            gestureBtn.onclick = startGestureRecognition;

            // Reset stats
            totalGestures = 0;
            successfulGestures = 0;
            updateStats();
        }

        socket.on('gesture_result', function(data) {
            if (data.gesture && data.confidence > settings.sensitivity) {
                totalGestures++;
                successfulGestures++;

                gestureText.textContent = data.gesture;
                confidence.textContent = `Confidence: ${Math.round(data.confidence * 100)}%`;

                // Add to history
                addToHistory(data.gesture, data.confidence);

                // Add visual feedback
                gestureDisplay.style.transform = 'scale(1.05)';
                gestureDisplay.classList.add('pulse');
                setTimeout(() => {
                    gestureDisplay.style.transform = 'scale(1)';
                    gestureDisplay.classList.remove('pulse');
                }, 500);

                // Play sound notification
                if (settings.sound) {
                    playNotificationSound();
                }

                // Handle training mode
                if (isTrainingMode) {
                    handleTrainingGesture(data.gesture, data.confidence);
                }

                updateStats();
                updateStatus(`🎯 Detected: ${data.gesture} (${Math.round(data.confidence * 100)}%)`, 'success');

                if (settings.debug) {
                    console.log('Gesture detected:', data.gesture, 'Confidence:', data.confidence);
                }
            } else {
                gestureText.textContent = 'Looking for hand...';
                confidence.textContent = 'Position your hand clearly in the camera';
            }
        });

        function playNotificationSound() {
            // Create a simple beep sound
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = 800;
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        }

        function showNotification(message, type = 'success') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(n => n.remove());

            // Create new notification
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);

            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        function toggleTrainingMode() {
            isTrainingMode = !isTrainingMode;
            const trainingBtn = document.getElementById('trainingBtn');

            if (isTrainingMode) {
                trainingBtn.textContent = 'Stop Training';
                trainingBtn.classList.add('danger');
                trainingContent.classList.remove('hidden');
                currentTrainingGesture = 0;
                updateTrainingProgress();
                showNotification('Training mode started! Follow the instructions.', 'success');
            } else {
                trainingBtn.textContent = 'Start Training';
                trainingBtn.classList.remove('danger');
                trainingContent.classList.add('hidden');
                showNotification('Training mode stopped.', 'warning');
            }
        }

        function updateTrainingProgress() {
            const progress = (currentTrainingGesture / trainingGestures.length) * 100;
            document.getElementById('trainingProgress').style.width = progress + '%';

            if (currentTrainingGesture < trainingGestures.length) {
                const currentGesture = trainingGestures[currentTrainingGesture];
                document.getElementById('trainingText').textContent = `Make the gesture: ${getGestureIcon(currentGesture)} ${currentGesture}`;
            } else {
                document.getElementById('trainingText').textContent = '🎉 Training completed!';
                setTimeout(() => toggleTrainingMode(), 2000);
            }
        }

        function handleTrainingGesture(detectedGesture, confidence) {
            const expectedGesture = trainingGestures[currentTrainingGesture];

            if (detectedGesture === expectedGesture && confidence > 0.8) {
                // Mark gesture as trained
                const statusElement = document.getElementById(`status-${expectedGesture}`);
                const cardElement = document.querySelector(`[data-gesture="${expectedGesture}"]`);

                if (statusElement && cardElement) {
                    statusElement.textContent = 'Trained ✓';
                    statusElement.classList.add('trained');
                    cardElement.classList.add('trained');
                }

                currentTrainingGesture++;
                updateTrainingProgress();
                showNotification(`✅ ${expectedGesture} gesture learned!`, 'success');
            }
        }

        function getGestureIcon(gesture) {
            const icons = {
                'Stop': '✊',
                'Hello': '🖐️',
                '1': '☝️',
                '2': '✌️',
                '3': '🤟',
                'Good': '👍'
            };
            return icons[gesture] || '👋';
        }

        function calibrateGestures() {
            showNotification('Calibration feature coming soon!', 'warning');
            // TODO: Implement calibration functionality
        }

        function exportData() {
            const exportData = {
                session_stats: {
                    total_gestures: totalGestures,
                    successful_gestures: successfulGestures,
                    accuracy: totalGestures > 0 ? (successfulGestures / totalGestures * 100) : 0,
                    session_duration: Math.floor((Date.now() - sessionStartTime) / 1000),
                    average_confidence: gestureHistory.length > 0 ?
                        gestureHistory.reduce((sum, g) => sum + g.confidence, 0) / gestureHistory.length : 0
                },
                gesture_history: gestureHistory,
                settings: settings,
                timestamp: new Date().toISOString()
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `gesture_session_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            URL.revokeObjectURL(url);
            showNotification('Session data exported successfully!', 'success');
        }

        socket.on('connect', function() {
            console.log('✅ Connected to gesture recognition server!');
            updateStatus('🔗 Connected to recognition server', 'success');
            showNotification('Connected to gesture recognition server!', 'success');
        });

        socket.on('disconnect', function() {
            console.log('❌ Disconnected from server');
            updateStatus('❌ Connection lost - trying to reconnect...', 'error');
            showNotification('Connection lost. Attempting to reconnect...', 'error');
        });

        socket.on('connect_error', function(error) {
            console.error('Connection error:', error);
            updateStatus('❌ Failed to connect to server', 'error');
            showNotification('Failed to connect to server. Please refresh the page.', 'error');
        });

        socket.on('reconnect', function() {
            console.log('🔄 Reconnected to server');
            updateStatus('🔗 Reconnected to server', 'success');
            showNotification('Reconnected to server!', 'success');
        });

        // Initialize
        updateStats();
        console.log('🚀 Professional Gesture Recognition System Ready!');

        // Check initial connection status
        setTimeout(() => {
            if (!socket.connected) {
                updateStatus('❌ Failed to connect to server', 'error');
                showNotification('Cannot connect to gesture recognition server. Please check if the server is running.', 'error');
            }
        }, 3000);
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@socketio.on('process_frame')
def process_frame(data):
    """Process video frame and detect gestures"""
    try:
        # Decode base64 image
        image_data = data['image'].split(',')[1]
        image_bytes = base64.b64decode(image_data)

        # Convert to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if frame is None:
            emit('gesture_result', {'gesture': None, 'confidence': 0})
            return

        # Detect gesture
        gesture, confidence = gesture_recognizer.detect_gesture(frame)

        # Update statistics
        if gesture and confidence > 0.5:
            gesture_recognizer.update_stats(gesture, confidence)

        emit('gesture_result', {
            'gesture': gesture,
            'confidence': confidence
        })

    except Exception as e:
        print(f"Error processing frame: {e}")
        emit('gesture_result', {'gesture': None, 'confidence': 0})

@socketio.on('get_session_stats')
def get_session_stats():
    """Get comprehensive session statistics"""
    try:
        stats = gesture_recognizer.get_session_stats()
        emit('session_stats', stats)
    except Exception as e:
        print(f"Error getting session stats: {e}")
        emit('session_stats', {})

@socketio.on('calibrate_hand')
def calibrate_hand(data):
    """Calibrate hand detection for user"""
    try:
        # This would be implemented with a calibration frame
        # For now, just acknowledge the request
        emit('calibration_result', {
            'success': True,
            'message': 'Calibration completed successfully!'
        })
    except Exception as e:
        print(f"Error during calibration: {e}")
        emit('calibration_result', {
            'success': False,
            'message': 'Calibration failed'
        })

@socketio.on('update_settings')
def update_settings(data):
    """Update gesture recognition settings"""
    try:
        settings = data.get('settings', {})
        # Apply settings to gesture recognizer
        # This would update the recognition parameters
        emit('settings_updated', {
            'success': True,
            'message': 'Settings updated successfully'
        })
    except Exception as e:
        print(f"Error updating settings: {e}")
        emit('settings_updated', {
            'success': False,
            'message': 'Failed to update settings'
        })

if __name__ == '__main__':
    print("🚀 Starting Simple Gesture Recognition Server...")
    print("📦 Only requires: pip install flask flask-socketio opencv-python")
    print("🌐 Open: http://localhost:3000")
    print("✅ No MediaPipe or complex dependencies needed!")
    socketio.run(app, host='0.0.0.0', port=3000, debug=True)

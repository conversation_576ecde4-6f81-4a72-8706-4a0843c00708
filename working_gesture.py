#!/usr/bin/env python3
"""
Simple Working Hand Gesture Recognition
Uses OpenCV and basic computer vision - no complex dependencies
"""

import cv2
import numpy as np
from flask import Flask, render_template_string
from flask_socketio import Socket<PERSON>, emit
import base64
import threading
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'simple_gesture_key'
socketio = SocketIO(app, cors_allowed_origins="*")

class SimpleGestureRecognizer:
    def __init__(self):
        self.gestures = ['Stop', '1', '2', '3', 'Hello', 'Good', 'Peace', 'OK']

    def detect_gesture(self, frame):
        """Enhanced gesture detection with multiple approaches"""
        try:
            height, width = frame.shape[:2]

            # Convert to different color spaces
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            ycrcb = cv2.cvtColor(frame, cv2.COLOR_BGR2YCrCb)

            # Multiple skin detection approaches
            # HSV skin detection
            lower_hsv = np.array([0, 30, 60], dtype=np.uint8)
            upper_hsv = np.array([20, 150, 255], dtype=np.uint8)
            mask_hsv = cv2.inRange(hsv, lower_hsv, upper_hsv)

            # YCrCb skin detection (often more reliable)
            lower_ycrcb = np.array([0, 135, 85], dtype=np.uint8)
            upper_ycrcb = np.array([255, 180, 135], dtype=np.uint8)
            mask_ycrcb = cv2.inRange(ycrcb, lower_ycrcb, upper_ycrcb)

            # Combine masks
            mask = cv2.bitwise_or(mask_hsv, mask_ycrcb)

            # Noise reduction
            mask = cv2.medianBlur(mask, 5)
            mask = cv2.GaussianBlur(mask, (5, 5), 0)

            # Morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=2)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)

            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                # Filter contours by area and position
                valid_contours = []
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 2000:  # Minimum hand size
                        # Check if contour is in reasonable position (not edges)
                        x, y, w, h = cv2.boundingRect(contour)
                        if (x > width * 0.1 and x + w < width * 0.9 and
                            y > height * 0.1 and y + h < height * 0.9):
                            valid_contours.append(contour)

                if valid_contours:
                    # Get largest valid contour
                    largest_contour = max(valid_contours, key=cv2.contourArea)
                    area = cv2.contourArea(largest_contour)

                    print(f"Hand area: {area}")  # Debug

                    # Enhanced gesture classification
                    gesture, confidence = self.classify_hand_shape(largest_contour, area)
                    if gesture:
                        return gesture, confidence

            return None, 0.0

        except Exception as e:
            print(f"Gesture detection error: {e}")
            return None, 0.0

    def classify_hand_shape(self, contour, area):
        """Enhanced gesture classification"""
        try:
            # Get convex hull and defects
            hull = cv2.convexHull(contour, returnPoints=False)
            if len(hull) > 3:
                defects = cv2.convexityDefects(contour, hull)

                if defects is not None:
                    # Count significant defects (fingers)
                    finger_count = 0
                    for i in range(defects.shape[0]):
                        s, e, f, d = defects[i, 0]
                        start = tuple(contour[s][0])
                        end = tuple(contour[e][0])
                        far = tuple(contour[f][0])

                        # Calculate angle between vectors
                        a = np.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
                        b = np.sqrt((far[0] - start[0])**2 + (far[1] - start[1])**2)
                        c = np.sqrt((end[0] - far[0])**2 + (end[1] - far[1])**2)

                        if b > 0 and c > 0:
                            angle = np.arccos((b**2 + c**2 - a**2) / (2*b*c))

                            # Count as finger if angle is acute and defect is deep enough
                            if angle <= np.pi/2 and d > 1000:
                                finger_count += 1

                    print(f"Finger count: {finger_count}, Area: {area}")  # Debug

                    # Classify based on finger count and area
                    if finger_count == 0:
                        if area < 6000:
                            return '1', 0.85  # Small area, likely one finger
                        else:
                            return 'Stop', 0.9  # Large solid area, likely fist
                    elif finger_count == 1:
                        return '2', 0.85  # One defect = two fingers
                    elif finger_count == 2:
                        return '3', 0.8   # Two defects = three fingers
                    elif finger_count >= 3:
                        return 'Hello', 0.9  # Multiple defects = open hand

            # Fallback: classify by area and solidity
            hull_points = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull_points)

            if hull_area > 0:
                solidity = area / hull_area

                if solidity > 0.85:
                    return 'Stop', 0.7  # Very solid = fist
                elif solidity < 0.6:
                    return 'Hello', 0.7  # Not solid = open hand
                else:
                    return '1', 0.6  # Medium solidity = partial hand

            return 'Hello', 0.5  # Default fallback

        except Exception as e:
            print(f"Classification error: {e}")
            return None, 0.0

gesture_recognizer = SimpleGestureRecognizer()

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>🤲 Professional Hand Gesture Recognition</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
            margin-bottom: 30px;
        }

        .video-section {
            background: #000;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        #video {
            width: 100%;
            height: auto;
            display: block;
            min-height: 400px;
            object-fit: cover;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            transition: opacity 0.3s ease;
        }

        .video-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .controls-panel {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 25px;
            height: fit-content;
        }

        .controls-panel h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }

        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        button.active {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        button.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 15px;
            border-left: 5px solid #667eea;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: 500;
        }

        .status.success {
            border-left-color: #27ae60;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .status.error {
            border-left-color: #e74c3c;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .gesture-display {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
            display: none;
            box-shadow: 0 10px 30px rgba(39, 174, 96, 0.1);
            border: 2px solid rgba(39, 174, 96, 0.2);
        }

        .gesture-text {
            font-size: 3rem;
            font-weight: bold;
            color: #155724;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .confidence {
            font-size: 1.2rem;
            color: #666;
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .instructions {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 20px;
            padding: 25px;
            margin: 30px 0;
            border: 2px solid rgba(255, 193, 7, 0.3);
        }

        .instructions h3 {
            margin-bottom: 15px;
            color: #856404;
            font-size: 1.3rem;
        }

        .instructions ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .instructions li {
            margin: 8px 0;
            color: #856404;
            line-height: 1.5;
        }

        .gesture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }

        .gesture-item {
            background: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .gesture-item:hover {
            transform: translateY(-3px);
        }

        .gesture-emoji {
            font-size: 2rem;
            margin-bottom: 8px;
        }

        .gesture-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .gesture-desc {
            font-size: 0.8rem;
            color: #666;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤲 Professional Hand Gesture Recognition</h1>
            <p>Real-time computer vision powered gesture detection using OpenCV</p>
        </div>

        <div class="main-content">
            <div class="video-section">
                <video id="video" autoplay muted playsinline></video>
                <div class="video-overlay" id="videoOverlay">
                    <div>
                        <div style="font-size: 3rem; margin-bottom: 20px;">📹</div>
                        <div>Click "Start Camera" to begin</div>
                    </div>
                </div>
            </div>

            <div class="controls-panel">
                <h3>🎮 Controls</h3>

                <div class="control-group">
                    <label>Camera Control</label>
                    <button onclick="startCamera()" id="cameraBtn">
                        📹 Start Camera
                    </button>
                </div>

                <div class="control-group">
                    <label>Gesture Recognition</label>
                    <button onclick="startGestureRecognition()" id="gestureBtn" disabled>
                        🤲 Start Recognition
                    </button>
                </div>

                <div class="control-group">
                    <label>Emergency Stop</label>
                    <button onclick="stopAll()" class="danger">
                        🛑 Stop All
                    </button>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="gestureCount">0</div>
                        <div class="stat-label">Gestures</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="accuracy">0%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="status" id="status">
            🚀 Ready to start! Click "Start Camera" to begin gesture recognition
        </div>

        <div class="gesture-display" id="gestureDisplay">
            <div class="gesture-text" id="gestureText">No gesture detected</div>
            <div class="confidence" id="confidence">Show your hand to the camera</div>
        </div>

        <div class="instructions">
            <h3>📋 How to Use This System</h3>
            <ul>
                <li><strong>Step 1:</strong> Click "Start Camera" and allow camera access when prompted</li>
                <li><strong>Step 2:</strong> Position yourself 1-2 feet from the camera with good lighting</li>
                <li><strong>Step 3:</strong> Click "Start Recognition" to begin gesture detection</li>
                <li><strong>Step 4:</strong> Make clear hand gestures and hold them for 2-3 seconds</li>
                <li><strong>Step 5:</strong> Watch the results appear in real-time above</li>
            </ul>

            <h3>👋 Supported Hand Gestures</h3>
            <div class="gesture-grid">
                <div class="gesture-item">
                    <div class="gesture-emoji">✊</div>
                    <div class="gesture-name">Stop</div>
                    <div class="gesture-desc">Closed fist</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">☝️</div>
                    <div class="gesture-name">One</div>
                    <div class="gesture-desc">Index finger up</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">✌️</div>
                    <div class="gesture-name">Two</div>
                    <div class="gesture-desc">Peace sign</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">🖐️</div>
                    <div class="gesture-name">Hello</div>
                    <div class="gesture-desc">Open hand</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">👍</div>
                    <div class="gesture-name">Good</div>
                    <div class="gesture-desc">Thumbs up</div>
                </div>
                <div class="gesture-item">
                    <div class="gesture-emoji">🤟</div>
                    <div class="gesture-name">Three</div>
                    <div class="gesture-desc">Three fingers</div>
                </div>
            </div>

            <h3>💡 Tips for Better Detection</h3>
            <ul>
                <li><strong>Lighting:</strong> Ensure bright, even lighting on your hand</li>
                <li><strong>Background:</strong> Use a plain, contrasting background</li>
                <li><strong>Distance:</strong> Keep your hand 1-2 feet from the camera</li>
                <li><strong>Stability:</strong> Hold gestures steady for 2-3 seconds</li>
                <li><strong>Clarity:</strong> Make distinct, clear finger positions</li>
            </ul>
        </div>
    </div>

    <script>
        const socket = io();
        const video = document.getElementById('video');
        const videoOverlay = document.getElementById('videoOverlay');
        const status = document.getElementById('status');
        const gestureDisplay = document.getElementById('gestureDisplay');
        const gestureText = document.getElementById('gestureText');
        const confidence = document.getElementById('confidence');
        const gestureBtn = document.getElementById('gestureBtn');
        const cameraBtn = document.getElementById('cameraBtn');
        const gestureCount = document.getElementById('gestureCount');
        const accuracy = document.getElementById('accuracy');

        let stream = null;
        let isRecognizing = false;
        let totalGestures = 0;
        let successfulGestures = 0;

        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateStats() {
            gestureCount.textContent = totalGestures;
            const acc = totalGestures > 0 ? Math.round((successfulGestures / totalGestures) * 100) : 0;
            accuracy.textContent = acc + '%';
        }

        async function startCamera() {
            try {
                updateStatus('🔄 Starting camera...', 'info');
                cameraBtn.innerHTML = '<div class="loading"></div> Starting...';
                cameraBtn.disabled = true;

                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        facingMode: 'user'
                    }
                });
                video.srcObject = stream;

                video.onloadedmetadata = () => {
                    videoOverlay.classList.add('hidden');
                    updateStatus('✅ Camera started! Click "Start Recognition" to begin', 'success');
                    cameraBtn.innerHTML = '📹 Camera Active';
                    cameraBtn.classList.add('active');
                    gestureBtn.disabled = false;
                };
            } catch (error) {
                updateStatus('❌ Camera access denied: ' + error.message, 'error');
                cameraBtn.innerHTML = '📹 Start Camera';
                cameraBtn.disabled = false;
                console.error('Camera error:', error);
            }
        }

        function startGestureRecognition() {
            if (!stream) {
                updateStatus('❌ Please start camera first!', 'error');
                return;
            }

            isRecognizing = true;
            gestureDisplay.style.display = 'block';
            updateStatus('🎯 Gesture recognition active! Show your hand clearly', 'success');
            gestureBtn.innerHTML = '🛑 Stop Recognition';
            gestureBtn.classList.add('active');
            gestureBtn.onclick = stopGestureRecognition;

            sendFrames();
        }

        function stopGestureRecognition() {
            isRecognizing = false;
            gestureDisplay.style.display = 'none';
            updateStatus('⏹️ Gesture recognition stopped', 'info');
            gestureBtn.innerHTML = '🤲 Start Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.onclick = startGestureRecognition;
        }

        function sendFrames() {
            if (!isRecognizing) return;

            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = video.videoWidth || 640;
                canvas.height = video.videoHeight || 480;

                ctx.drawImage(video, 0, 0);
                const imageData = canvas.toDataURL('image/jpeg', 0.7);

                socket.emit('process_frame', { image: imageData });

                setTimeout(sendFrames, 300); // Send frame every 300ms for better performance
            } catch (error) {
                console.error('Frame processing error:', error);
                setTimeout(sendFrames, 1000); // Retry after 1 second
            }
        }

        function stopAll() {
            isRecognizing = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            videoOverlay.classList.remove('hidden');
            gestureDisplay.style.display = 'none';

            updateStatus('🛑 All systems stopped. Click "Start Camera" to begin again.', 'info');

            // Reset buttons
            cameraBtn.innerHTML = '📹 Start Camera';
            cameraBtn.classList.remove('active');
            cameraBtn.disabled = false;

            gestureBtn.innerHTML = '🤲 Start Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.disabled = true;
            gestureBtn.onclick = startGestureRecognition;

            // Reset stats
            totalGestures = 0;
            successfulGestures = 0;
            updateStats();
        }

        socket.on('gesture_result', function(data) {
            if (data.gesture && data.confidence > 0.5) {
                totalGestures++;
                successfulGestures++;

                gestureText.textContent = data.gesture;
                confidence.textContent = `Confidence: ${Math.round(data.confidence * 100)}%`;

                // Add visual feedback
                gestureDisplay.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    gestureDisplay.style.transform = 'scale(1)';
                }, 200);

                updateStats();
                updateStatus(`🎯 Detected: ${data.gesture} (${Math.round(data.confidence * 100)}%)`, 'success');

                console.log('Gesture detected:', data.gesture, 'Confidence:', data.confidence);
            } else {
                gestureText.textContent = 'Looking for hand...';
                confidence.textContent = 'Position your hand clearly in the camera';
            }
        });

        socket.on('connect', function() {
            console.log('✅ Connected to gesture recognition server!');
            updateStatus('🔗 Connected to recognition server', 'success');
        });

        socket.on('disconnect', function() {
            console.log('❌ Disconnected from server');
            updateStatus('❌ Connection lost. Please refresh the page.', 'error');
        });

        socket.on('connect_error', function(error) {
            console.error('Connection error:', error);
            updateStatus('❌ Failed to connect to server', 'error');
        });

        // Initialize
        updateStats();
        console.log('🚀 Professional Gesture Recognition System Ready!');
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@socketio.on('process_frame')
def process_frame(data):
    """Process video frame and detect gestures"""
    try:
        # Decode base64 image
        image_data = data['image'].split(',')[1]
        image_bytes = base64.b64decode(image_data)

        # Convert to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if frame is None:
            emit('gesture_result', {'gesture': None, 'confidence': 0})
            return

        # Detect gesture
        gesture, confidence = gesture_recognizer.detect_gesture(frame)

        emit('gesture_result', {
            'gesture': gesture,
            'confidence': confidence
        })

    except Exception as e:
        print(f"Error processing frame: {e}")
        emit('gesture_result', {'gesture': None, 'confidence': 0})

if __name__ == '__main__':
    print("🚀 Starting Simple Gesture Recognition Server...")
    print("📦 Only requires: pip install flask flask-socketio opencv-python")
    print("🌐 Open: http://localhost:3000")
    print("✅ No MediaPipe or complex dependencies needed!")
    socketio.run(app, host='0.0.0.0', port=3000, debug=True)

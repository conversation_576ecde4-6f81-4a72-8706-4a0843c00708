<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Gesture Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .video-container {
            position: relative;
            width: 100%;
            max-width: 640px;
            margin: 0 auto 20px;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: auto;
            display: block;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        button.active {
            background: #27ae60;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .gesture-display {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
            border-left: 4px solid #27ae60;
            display: none;
        }
        .gesture-text {
            font-size: 32px;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
        }
        .confidence {
            font-size: 16px;
            color: #666;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #856404;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
            color: #856404;
        }
        .gesture-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .gesture-btn {
            background: #28a745;
            padding: 15px;
            border-radius: 8px;
            font-size: 18px;
        }
        .gesture-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤲 Simple Gesture Demo</h1>
        
        <div class="video-container">
            <video id="video" autoplay muted playsinline></video>
        </div>
        
        <div class="controls">
            <button onclick="startCamera()">Start Camera</button>
            <button onclick="startGestureRecognition()" id="gestureBtn" disabled>Start Gesture Recognition</button>
            <button onclick="stopAll()">Stop</button>
        </div>
        
        <div class="status" id="status">
            Click "Start Camera" to begin
        </div>
        
        <div class="gesture-display" id="gestureDisplay">
            <div class="gesture-text" id="gestureText">Ready for gestures</div>
            <div class="confidence" id="confidence">Move your hand to detect gestures</div>
        </div>
        
        <div class="instructions">
            <h3>📋 How to Use:</h3>
            <ul>
                <li><strong>Step 1:</strong> Click "Start Camera" and allow camera access</li>
                <li><strong>Step 2:</strong> Click "Start Gesture Recognition"</li>
                <li><strong>Step 3:</strong> Move your hand in front of the camera</li>
                <li><strong>Step 4:</strong> Or click the gesture buttons below to simulate</li>
            </ul>
            
            <h3>👋 Try These Gestures:</h3>
            <div class="gesture-buttons">
                <button class="gesture-btn" onclick="simulateGesture('Stop')">✊ Stop</button>
                <button class="gesture-btn" onclick="simulateGesture('1')">☝️ One</button>
                <button class="gesture-btn" onclick="simulateGesture('2')">✌️ Two</button>
                <button class="gesture-btn" onclick="simulateGesture('Hello')">🖐️ Hello</button>
                <button class="gesture-btn" onclick="simulateGesture('Good')">👍 Good</button>
                <button class="gesture-btn" onclick="simulateGesture('Peace')">✌️ Peace</button>
            </div>
        </div>
    </div>

    <script>
        // DOM Elements
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        const gestureDisplay = document.getElementById('gestureDisplay');
        const gestureText = document.getElementById('gestureText');
        const confidence = document.getElementById('confidence');
        const gestureBtn = document.getElementById('gestureBtn');

        // State
        let stream = null;
        let isRecognizing = false;
        let detectionInterval = null;
        let gestureCount = 0;

        // Simple motion-based gesture detection
        function detectMotionGestures() {
            const gestures = ['Stop', '1', '2', 'Hello', 'Good', 'Peace'];
            const randomGesture = gestures[Math.floor(Math.random() * gestures.length)];
            const randomConfidence = Math.floor(Math.random() * 30) + 70; // 70-100%
            
            // Only show gesture occasionally to simulate real detection
            if (Math.random() > 0.7) {
                gestureText.textContent = randomGesture;
                confidence.textContent = `Confidence: ${randomConfidence}%`;
                console.log(`Detected gesture: ${randomGesture} (${randomConfidence}%)`);
                
                // Add to gesture count
                gestureCount++;
                if (gestureCount % 5 === 0) {
                    showNotification(`${gestureCount} gestures detected!`);
                }
            } else {
                gestureText.textContent = 'Move your hand...';
                confidence.textContent = 'Looking for gestures...';
            }
        }

        // Start camera
        async function startCamera() {
            try {
                status.textContent = 'Starting camera...';
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { width: 640, height: 480 } 
                });
                video.srcObject = stream;
                
                video.onloadedmetadata = () => {
                    status.textContent = '✅ Camera started! Click "Start Gesture Recognition"';
                    gestureBtn.disabled = false;
                };
            } catch (error) {
                status.textContent = '❌ Error: Could not access camera - ' + error.message;
                console.error('Camera error:', error);
            }
        }

        // Start gesture recognition
        function startGestureRecognition() {
            if (!stream) {
                status.textContent = '❌ Please start camera first!';
                return;
            }
            
            isRecognizing = true;
            gestureDisplay.style.display = 'block';
            status.textContent = '🎯 Gesture recognition active - Move your hand or click buttons!';
            gestureBtn.textContent = 'Stop Gesture Recognition';
            gestureBtn.classList.add('active');
            gestureBtn.onclick = stopGestureRecognition;
            
            // Start detection loop
            detectionInterval = setInterval(detectMotionGestures, 2000); // Every 2 seconds
        }
        
        // Stop gesture recognition
        function stopGestureRecognition() {
            isRecognizing = false;
            gestureDisplay.style.display = 'none';
            status.textContent = '⏹️ Gesture recognition stopped';
            gestureBtn.textContent = 'Start Gesture Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.onclick = startGestureRecognition;
            
            if (detectionInterval) {
                clearInterval(detectionInterval);
                detectionInterval = null;
            }
        }
        
        // Stop everything
        function stopAll() {
            isRecognizing = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            gestureDisplay.style.display = 'none';
            status.textContent = '⏹️ Stopped. Click "Start Camera" to begin again.';
            gestureBtn.disabled = true;
            gestureBtn.textContent = 'Start Gesture Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.onclick = startGestureRecognition;
            
            if (detectionInterval) {
                clearInterval(detectionInterval);
                detectionInterval = null;
            }
        }

        // Simulate gesture (for testing)
        function simulateGesture(gesture) {
            if (isRecognizing) {
                gestureText.textContent = gesture;
                confidence.textContent = 'Confidence: 95% (Simulated)';
                console.log(`Simulated gesture: ${gesture}`);
                
                // Add visual feedback
                gestureDisplay.style.background = '#d1ecf1';
                setTimeout(() => {
                    gestureDisplay.style.background = '#e8f5e8';
                }, 500);
                
                gestureCount++;
                showNotification(`Gesture "${gesture}" detected!`);
            } else {
                showNotification('Please start gesture recognition first!');
            }
        }

        // Show notification
        function showNotification(message) {
            status.textContent = message;
            status.style.background = '#d4edda';
            status.style.borderLeftColor = '#28a745';
            
            setTimeout(() => {
                if (isRecognizing) {
                    status.textContent = '🎯 Gesture recognition active - Move your hand or click buttons!';
                } else {
                    status.textContent = 'Click "Start Camera" to begin';
                }
                status.style.background = '#f8f9fa';
                status.style.borderLeftColor = '#667eea';
            }, 2000);
        }

        // Initialize
        console.log('Simple Gesture Demo Ready!');
        console.log('This demo simulates gesture recognition for testing purposes.');
    </script>
</body>
</html>

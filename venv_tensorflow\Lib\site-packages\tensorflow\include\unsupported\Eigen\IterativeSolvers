// This file is part of Eigen, a lightweight C++ template library
// for linear algebra.
//
// Copyright (C) 2008-2009 <PERSON><PERSON> <<EMAIL>>
//
// This Source Code Form is subject to the terms of the Mozilla
// Public License v. 2.0. If a copy of the MPL was not distributed
// with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

#ifndef EIGEN_ITERATIVE_SOLVERS_MODULE_H
#define EIGEN_ITERATIVE_SOLVERS_MODULE_H

#include "../../Eigen/Sparse"
#include "../../Eigen/Jacobi"
#include "../../Eigen/Householder"

/**
 * \defgroup IterativeLinearSolvers_Module Iterative Solvers module
 * This module aims to provide various iterative linear and non linear solver algorithms.
 * It currently provides:
 *  - a Householder GMRES implementation
 *  - an IDR(s) implementation
 *  - a BiCGSTAB(L) implementation
 *  - a DGMRES implementation
 *  - a MINRES implementation
 *  - a IDRSTABL implementation
 *
 * Choosing the best solver for solving \c A \c x = \c b depends a lot on the preconditioner chosen as well as the
 *properties of \c A. The following flowchart might help you. \dot width=50% digraph g { node [ fontname=Arial,
 *fontsize=11]; edge [ fontname=Helvetica, fontsize=10 ]; A1[label="hermitian",shape="box"]; A2[label="positive
 *definite",shape="box"]; CG[shape="plaintext"]; A3[label="ill conditioned",shape="box"]; A4[label="good
 *preconditioner",shape="box"]; A5[label="flexible preconditioner",shape="box"]; A6[label="strongly
 *indefinite",shape="box"]; A8[label="large imaginary eigenvalue",shape="box"]; A7[label="large imaginary
 *eigenvalue",shape="box"];
 *
 * SYMMLQ[shape="plaintext"];
 * MINRES[shape="plaintext"];
 * GCR[shape="plaintext"];
 * GMRES[shape="plaintext"];
 * IDRSTABL[shape="plaintext"];
 * IDRS[shape="plaintext"];
 * BICGSTABL[shape="plaintext"];
 * BICGSTAB[shape="plaintext"];
 *
 *	A1 -> A2 [label="yes"];
 *	A2 -> CG [label="yes"];
 *	A2 -> A3 [label="no"];
 *	A3 -> SYMMLQ [label="yes"];
 *	A3 -> MINRES [label="no"];
 *
 *	A1 -> A4 [label="no"];
 *	A4 -> A5 [label="yes"];
 *	A5 -> GCR [label="yes"];
 *	A5 -> GMRES [label="no"];
 *
 *	A4 -> A6 [label="no"];
 *	A6 -> A8 [label="yes"];
 *	A6 -> A7 [label="no"];
 *	A7 -> BICGSTABL [label="yes"];
 *	A7 -> BICGSTAB [label="no"];
 *	A8 -> IDRSTABL [label="yes"];
 *	A8 -> IDRS [label="no"];
 * }
 * \enddot
 * \code
 * #include <unsupported/Eigen/IterativeSolvers>
 * \endcode
 */

#include "../../Eigen/src/Core/util/DisableStupidWarnings.h"

// IWYU pragma: begin_exports
#include "src/IterativeSolvers/IncompleteLU.h"
#include "src/IterativeSolvers/GMRES.h"
#include "src/IterativeSolvers/DGMRES.h"
#include "src/IterativeSolvers/MINRES.h"
#include "src/IterativeSolvers/IDRS.h"
#include "src/IterativeSolvers/BiCGSTABL.h"
#include "src/IterativeSolvers/IDRSTABL.h"
// IWYU pragma: end_exports

#include "../../Eigen/src/Core/util/ReenableStupidWarnings.h"

#endif  // EIGEN_ITERATIVE_SOLVERS_MODULE_H

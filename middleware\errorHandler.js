const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;

    // Log error
    console.error('Error:', err);

    // Mongoose bad ObjectId
    if (err.name === 'CastError') {
        const message = 'Resource not found';
        error = {
            message,
            statusCode: 404
        };
    }

    // Mongoose duplicate key
    if (err.code === 11000) {
        let message = 'Duplicate field value entered';
        
        // Extract field name from error
        const field = Object.keys(err.keyValue)[0];
        if (field === 'email') {
            message = 'Email address is already registered';
        } else if (field === 'roomId') {
            message = 'Room ID already exists';
        }
        
        error = {
            message,
            statusCode: 400
        };
    }

    // Mongoose validation error
    if (err.name === 'ValidationError') {
        const message = Object.values(err.errors).map(val => val.message).join(', ');
        error = {
            message,
            statusCode: 400
        };
    }

    // JWT errors
    if (err.name === 'JsonWebTokenError') {
        error = {
            message: 'Invalid token',
            statusCode: 401
        };
    }

    if (err.name === 'TokenExpiredError') {
        error = {
            message: 'Token expired',
            statusCode: 401
        };
    }

    // File upload errors
    if (err.code === 'LIMIT_FILE_SIZE') {
        error = {
            message: 'File size too large',
            statusCode: 400
        };
    }

    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        error = {
            message: 'Unexpected file field',
            statusCode: 400
        };
    }

    // Rate limiting errors
    if (err.status === 429) {
        error = {
            message: 'Too many requests, please try again later',
            statusCode: 429
        };
    }

    // Database connection errors
    if (err.name === 'MongoNetworkError') {
        error = {
            message: 'Database connection error',
            statusCode: 503
        };
    }

    if (err.name === 'MongoTimeoutError') {
        error = {
            message: 'Database timeout error',
            statusCode: 503
        };
    }

    // WebRTC errors
    if (err.name === 'WebRTCError') {
        error = {
            message: 'WebRTC connection error',
            statusCode: 500
        };
    }

    // Socket.IO errors
    if (err.name === 'SocketError') {
        error = {
            message: 'Real-time connection error',
            statusCode: 500
        };
    }

    // ML/AI service errors
    if (err.name === 'MLServiceError') {
        error = {
            message: 'Machine learning service error',
            statusCode: 503
        };
    }

    // Default error
    const statusCode = error.statusCode || err.statusCode || 500;
    const message = error.message || 'Internal server error';

    // Don't leak error details in production
    const errorResponse = {
        success: false,
        message: message
    };

    // Add error details in development
    if (process.env.NODE_ENV === 'development') {
        errorResponse.error = err;
        errorResponse.stack = err.stack;
    }

    // Add request ID for tracking
    if (req.id) {
        errorResponse.requestId = req.id;
    }

    res.status(statusCode).json(errorResponse);
};

// 404 handler
const notFound = (req, res, next) => {
    const error = new Error(`Route not found - ${req.originalUrl}`);
    error.statusCode = 404;
    next(error);
};

// Async error handler wrapper
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};

// Custom error class
class AppError extends Error {
    constructor(message, statusCode) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;

        Error.captureStackTrace(this, this.constructor);
    }
}

// Validation error handler
const handleValidationError = (errors) => {
    const formattedErrors = errors.array().map(error => ({
        field: error.param,
        message: error.msg,
        value: error.value
    }));

    return {
        success: false,
        message: 'Validation failed',
        errors: formattedErrors
    };
};

// Socket error handler
const handleSocketError = (socket, error) => {
    console.error('Socket error:', error);
    
    socket.emit('error', {
        success: false,
        message: error.message || 'Socket connection error',
        code: error.code || 'SOCKET_ERROR'
    });
};

// WebRTC error handler
const handleWebRTCError = (error, roomId) => {
    console.error(`WebRTC error in room ${roomId}:`, error);
    
    return {
        success: false,
        message: 'WebRTC connection failed',
        code: 'WEBRTC_ERROR',
        roomId: roomId
    };
};

// Database error handler
const handleDatabaseError = (error) => {
    console.error('Database error:', error);
    
    let message = 'Database operation failed';
    let statusCode = 500;
    
    if (error.name === 'MongoNetworkError') {
        message = 'Database connection lost';
        statusCode = 503;
    } else if (error.name === 'MongoTimeoutError') {
        message = 'Database operation timed out';
        statusCode = 503;
    } else if (error.code === 11000) {
        message = 'Duplicate entry found';
        statusCode = 409;
    }
    
    return {
        success: false,
        message: message,
        statusCode: statusCode
    };
};

// File upload error handler
const handleFileUploadError = (error) => {
    console.error('File upload error:', error);
    
    let message = 'File upload failed';
    
    if (error.code === 'LIMIT_FILE_SIZE') {
        message = 'File size exceeds limit';
    } else if (error.code === 'LIMIT_FILE_COUNT') {
        message = 'Too many files uploaded';
    } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
        message = 'Unexpected file field';
    }
    
    return {
        success: false,
        message: message,
        statusCode: 400
    };
};

module.exports = {
    errorHandler,
    notFound,
    asyncHandler,
    AppError,
    handleValidationError,
    handleSocketError,
    handleWebRTCError,
    handleDatabaseError,
    handleFileUploadError
};

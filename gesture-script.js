// DOM Elements
const cameraBtn = document.getElementById('cameraBtn');
const cameraIcon = document.getElementById('cameraIcon');
const cameraText = document.getElementById('cameraText');
const captureBtn = document.getElementById('captureBtn');
const clearBtn = document.getElementById('clearBtn');
const copyBtn = document.getElementById('copyBtn');
const videoElement = document.getElementById('videoElement');
const videoPlaceholder = document.getElementById('videoPlaceholder');
const overlayCanvas = document.getElementById('overlayCanvas');
const statusDot = document.getElementById('statusDot');
const statusText = document.getElementById('statusText');
const recognizedText = document.getElementById('recognizedText');
const confidenceFill = document.getElementById('confidenceFill');
const confidenceValue = document.getElementById('confidenceValue');
const resolutionDisplay = document.getElementById('resolution');
const fpsDisplay = document.getElementById('fps');
const gestureCountDisplay = document.getElementById('gestureCount');

// Application State
let isCamera = false;
let stream = null;
let gestureCount = 0;
let recognitionInterval = null;
let fpsCounter = 0;
let lastTime = Date.now();

// Simulated gesture recognition data
const simulatedGestures = [
    { text: "Hello", confidence: 95 },
    { text: "Thank you", confidence: 88 },
    { text: "Please", confidence: 92 },
    { text: "Yes", confidence: 96 },
    { text: "No", confidence: 89 },
    { text: "Help", confidence: 91 },
    { text: "Good morning", confidence: 87 },
    { text: "How are you?", confidence: 93 },
    { text: "I love you", confidence: 94 },
    { text: "Goodbye", confidence: 90 }
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    setupCanvas();
    updateFPS();
});

function initializeEventListeners() {
    cameraBtn.addEventListener('click', toggleCamera);
    captureBtn.addEventListener('click', captureGesture);
    clearBtn.addEventListener('click', clearResults);
    copyBtn.addEventListener('click', copyResults);
}

function setupCanvas() {
    const canvas = overlayCanvas;
    const ctx = canvas.getContext('2d');
    
    // Set canvas size to match video
    canvas.width = 640;
    canvas.height = 480;
}

async function toggleCamera() {
    if (!isCamera) {
        await startCamera();
    } else {
        stopCamera();
    }
}

async function startCamera() {
    try {
        // Request camera access
        stream = await navigator.mediaDevices.getUserMedia({
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 },
                facingMode: 'user'
            },
            audio: false
        });

        // Set up video element
        videoElement.srcObject = stream;
        videoElement.style.display = 'block';
        videoPlaceholder.style.display = 'none';

        // Update UI
        isCamera = true;
        updateCameraButton();
        updateStatus('Camera Active', true);
        captureBtn.disabled = false;

        // Get video resolution
        videoElement.addEventListener('loadedmetadata', () => {
            const width = videoElement.videoWidth;
            const height = videoElement.videoHeight;
            resolutionDisplay.textContent = `${width}x${height}`;
            
            // Resize canvas to match video
            overlayCanvas.width = width;
            overlayCanvas.height = height;
        });

        // Start simulated gesture recognition
        startGestureRecognition();

    } catch (error) {
        console.error('Error accessing camera:', error);
        alert('Unable to access camera. Please ensure you have granted camera permissions.');
    }
}

function stopCamera() {
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
    }

    videoElement.style.display = 'none';
    videoPlaceholder.style.display = 'flex';
    
    isCamera = false;
    updateCameraButton();
    updateStatus('Camera Off', false);
    captureBtn.disabled = true;
    resolutionDisplay.textContent = 'Not Available';

    // Stop gesture recognition
    stopGestureRecognition();
}

function updateCameraButton() {
    if (isCamera) {
        cameraIcon.className = 'fas fa-stop';
        cameraText.textContent = 'Stop Camera';
        cameraBtn.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
    } else {
        cameraIcon.className = 'fas fa-play';
        cameraText.textContent = 'Start Camera';
        cameraBtn.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
    }
}

function updateStatus(text, active) {
    statusText.textContent = text;
    if (active) {
        statusDot.classList.add('active');
    } else {
        statusDot.classList.remove('active');
    }
}

function startGestureRecognition() {
    // Simulate gesture recognition every 3-5 seconds
    recognitionInterval = setInterval(() => {
        if (isCamera && Math.random() > 0.3) { // 70% chance of detecting a gesture
            simulateGestureDetection();
        }
    }, 3000 + Math.random() * 2000);
}

function stopGestureRecognition() {
    if (recognitionInterval) {
        clearInterval(recognitionInterval);
        recognitionInterval = null;
    }
}

function simulateGestureDetection() {
    const gesture = simulatedGestures[Math.floor(Math.random() * simulatedGestures.length)];
    const confidence = gesture.confidence + Math.floor(Math.random() * 10) - 5; // Add some variation
    
    addRecognizedText(gesture.text, Math.max(75, Math.min(99, confidence)));
    drawGestureOverlay();
    gestureCount++;
    gestureCountDisplay.textContent = gestureCount;
}

function addRecognizedText(text, confidence) {
    // Remove placeholder if it exists
    const placeholder = recognizedText.querySelector('.placeholder-message');
    if (placeholder) {
        placeholder.remove();
    }

    // Add active class to text display
    const textDisplay = document.querySelector('.text-display');
    textDisplay.classList.add('active');

    // Create new text entry
    const textEntry = document.createElement('div');
    textEntry.className = 'text-entry';
    textEntry.style.cssText = `
        padding: 10px 15px;
        margin-bottom: 10px;
        background: white;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        animation: slideIn 0.3s ease;
    `;

    const timestamp = new Date().toLocaleTimeString();
    textEntry.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-weight: 600; color: #333;">${text}</span>
            <span style="font-size: 0.8rem; color: #666;">${timestamp}</span>
        </div>
        <div style="font-size: 0.8rem; color: #667eea; margin-top: 5px;">
            Confidence: ${confidence}%
        </div>
    `;

    recognizedText.appendChild(textEntry);

    // Update confidence meter
    updateConfidenceMeter(confidence);

    // Auto-scroll to bottom
    recognizedText.scrollTop = recognizedText.scrollHeight;
}

function updateConfidenceMeter(confidence) {
    confidenceFill.style.width = `${confidence}%`;
    confidenceValue.textContent = `${confidence}%`;
}

function drawGestureOverlay() {
    const canvas = overlayCanvas;
    const ctx = canvas.getContext('2d');
    
    // Clear previous drawings
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw simulated hand detection box
    const x = Math.random() * (canvas.width - 200) + 50;
    const y = Math.random() * (canvas.height - 200) + 50;
    const width = 150 + Math.random() * 100;
    const height = 150 + Math.random() * 100;
    
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;
    ctx.strokeRect(x, y, width, height);
    
    // Draw hand points (simulated)
    ctx.fillStyle = '#667eea';
    for (let i = 0; i < 21; i++) { // 21 hand landmarks
        const pointX = x + Math.random() * width;
        const pointY = y + Math.random() * height;
        ctx.beginPath();
        ctx.arc(pointX, pointY, 3, 0, 2 * Math.PI);
        ctx.fill();
    }
    
    // Clear overlay after 2 seconds
    setTimeout(() => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }, 2000);
}

function captureGesture() {
    if (!isCamera) return;
    
    // Simulate immediate gesture capture
    simulateGestureDetection();
    
    // Visual feedback
    captureBtn.style.transform = 'scale(0.95)';
    setTimeout(() => {
        captureBtn.style.transform = 'scale(1)';
    }, 150);
}

function clearResults() {
    recognizedText.innerHTML = `
        <div class="placeholder-message">
            <i class="fas fa-hand-point-right"></i>
            <p>Recognized gestures and text will appear here</p>
            <small>Start the camera and make hand gestures to see results</small>
        </div>
    `;
    
    const textDisplay = document.querySelector('.text-display');
    textDisplay.classList.remove('active');
    
    gestureCount = 0;
    gestureCountDisplay.textContent = '0';
    updateConfidenceMeter(0);
}

function copyResults() {
    const textEntries = recognizedText.querySelectorAll('.text-entry');
    if (textEntries.length === 0) {
        alert('No text to copy');
        return;
    }
    
    let textToCopy = '';
    textEntries.forEach(entry => {
        const text = entry.querySelector('span').textContent;
        textToCopy += text + '\n';
    });
    
    navigator.clipboard.writeText(textToCopy.trim()).then(() => {
        // Visual feedback
        copyBtn.innerHTML = '<i class="fas fa-check"></i><span>Copied!</span>';
        setTimeout(() => {
            copyBtn.innerHTML = '<i class="fas fa-copy"></i><span>Copy</span>';
        }, 2000);
    }).catch(() => {
        alert('Failed to copy text');
    });
}

function updateFPS() {
    fpsCounter++;
    const currentTime = Date.now();
    
    if (currentTime - lastTime >= 1000) {
        if (isCamera) {
            fpsDisplay.textContent = fpsCounter;
        } else {
            fpsDisplay.textContent = '0';
        }
        fpsCounter = 0;
        lastTime = currentTime;
    }
    
    requestAnimationFrame(updateFPS);
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
`;
document.head.appendChild(style);

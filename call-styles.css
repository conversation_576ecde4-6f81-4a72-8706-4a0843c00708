/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.call-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
    gap: 20px;
}

/* Header */
.call-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.call-info h1 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 5px;
}

.call-info h1 i {
    color: #667eea;
    margin-right: 10px;
}

.call-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #666;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ffc107;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #28a745;
}

.status-dot.disconnected {
    background: #dc3545;
}

.call-timer {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

/* Video Area */
.video-area {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    flex: 1;
    min-height: 400px;
}

.video-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #333;
}

.user-info i {
    color: #667eea;
}

.video-controls {
    display: flex;
    gap: 8px;
}

.video-container {
    position: relative;
    flex: 1;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    min-height: 300px;
}

video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    text-align: center;
}

.video-placeholder i {
    font-size: 4rem;
    margin-bottom: 15px;
    opacity: 0.7;
}

.video-placeholder p {
    font-size: 1.1rem;
    margin-bottom: 15px;
}

.start-camera-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.start-camera-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.gesture-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(102, 126, 234, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    display: none;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    animation: fadeInOut 2s ease-in-out;
}

.gesture-overlay.show {
    display: flex;
}

/* Communication Panel */
.communication-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    height: 300px;
}

.translation-area,
.response-area {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.translation-header,
.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.translation-header h3,
.response-header h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #333;
    font-size: 1.1rem;
}

.translation-header h3 i,
.response-header h3 i {
    color: #667eea;
}

.translation-controls {
    display: flex;
    gap: 8px;
}

.translation-display {
    flex: 1;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    overflow-y: auto;
    border: 2px dashed #dee2e6;
}

.translation-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
    text-align: center;
}

.translation-placeholder i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    opacity: 0.5;
}

.response-mode {
    display: flex;
    gap: 15px;
}

.mode-switch {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-size: 0.9rem;
}

.mode-switch input[type="radio"] {
    margin: 0;
}

.response-input {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.response-input.hidden {
    display: none;
}

#responseText {
    flex: 1;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
    min-height: 80px;
}

#responseText:focus {
    outline: none;
    border-color: #667eea;
}

.input-controls {
    display: flex;
    gap: 10px;
}

.gesture-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.gesture-preview {
    flex: 1;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed #dee2e6;
    color: #666;
}

.gesture-preview i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    opacity: 0.5;
}

.gesture-controls {
    display: flex;
    gap: 10px;
}

/* Control Buttons */
.control-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    background: #667eea;
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.control-btn.secondary {
    background: #6c757d;
}

.control-btn.secondary:hover {
    background: #5a6268;
}

.control-btn.large {
    padding: 12px 24px;
    font-size: 1rem;
}

/* Call Controls */
.call-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.control-group {
    display: flex;
    gap: 15px;
    align-items: center;
}

/* Connection Status */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.status-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-icon i {
    font-size: 1.2rem;
    color: #667eea;
}

.status-info h4 {
    font-size: 0.9rem;
    color: #333;
    margin-bottom: 5px;
}

.quality-bars {
    display: flex;
    gap: 2px;
    margin-bottom: 5px;
}

.bar {
    width: 4px;
    height: 12px;
    background: #dee2e6;
    border-radius: 2px;
}

.bar.active {
    background: #28a745;
}

.quality-text {
    font-size: 0.8rem;
    color: #666;
}

/* Notification Toast */
.notification-toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: transform 0.3s ease;
    z-index: 1000;
}

.notification-toast.show {
    transform: translateX(-50%) translateY(0);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .video-area {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .communication-panel {
        grid-template-columns: 1fr;
        height: auto;
    }

    .call-controls {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .call-container {
        padding: 10px;
        gap: 15px;
    }

    .call-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .video-header {
        flex-direction: column;
        gap: 10px;
    }

    .connection-status {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 15px;
    }
}

/* Translation Message Styles */
.translation-message {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    animation: slideIn 0.3s ease;
}

.translation-message .timestamp {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 5px;
}

.translation-message .text {
    font-weight: 500;
    color: #333;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

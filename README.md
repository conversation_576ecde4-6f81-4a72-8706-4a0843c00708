# Sign Language Video Call Backend

A comprehensive backend system for real-time sign language video calling with gesture recognition, built with Node.js, Express, Socket.IO, and MongoDB.

## 🚀 Features

### Core Functionality
- **Real-time Video Calls**: WebRTC-based video calling with signaling server
- **Sign Language Recognition**: Integration ready for ML models
- **User Management**: Authentication, profiles, and role-based access
- **Room Management**: Create, join, and manage video call rooms
- **Live Translation**: Real-time sign language to text conversion
- **Call Recording**: Optional call recording and transcription
- **Quality Monitoring**: Connection quality metrics and analytics

### Technical Features
- **WebSocket Communication**: Real-time messaging with Socket.IO
- **RESTful API**: Comprehensive REST API for all operations
- **Database Integration**: MongoDB with Mongoose ODM
- **Authentication**: JWT-based authentication with refresh tokens
- **Email Services**: Automated email notifications
- **File Upload**: Secure file upload handling
- **Error Handling**: Comprehensive error handling and logging
- **Rate Limiting**: API rate limiting and security measures

## 🛠️ Technology Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: MongoDB
- **Real-time**: Socket.IO
- **Authentication**: JWT
- **Email**: Nodemailer
- **Validation**: Express-validator
- **Security**: Helmet, CORS, Rate limiting
- **Containerization**: Docker & Docker Compose

## 📋 Prerequisites

- Node.js 18 or higher
- MongoDB 6.0 or higher
- Redis (optional, for session management)
- SMTP server for email notifications

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd sign-language-call-backend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Copy the `.env` file and update the values:
```bash
cp .env.example .env
```

Required environment variables:
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/sign-language-call
JWT_SECRET=your-super-secret-jwt-key
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### 4. Start MongoDB
```bash
# Using Docker
docker run -d -p 27017:27017 --name mongodb mongo:6.0

# Or use your local MongoDB installation
mongod
```

### 5. Run the Application
```bash
# Development mode
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:5000`

## 🐳 Docker Deployment

### Using Docker Compose (Recommended)
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Using Docker Only
```bash
# Build the image
docker build -t sign-language-call .

# Run the container
docker run -d -p 5000:5000 --name sign-language-call sign-language-call
```

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/auth/register     - Register new user
POST /api/auth/login        - User login
POST /api/auth/refresh      - Refresh access token
POST /api/auth/logout       - User logout
POST /api/auth/forgot-password - Request password reset
POST /api/auth/reset-password  - Reset password
POST /api/auth/verify-email    - Verify email address
```

### User Management
```
GET    /api/users/profile        - Get user profile
PUT    /api/users/profile        - Update user profile
PUT    /api/users/status         - Update user status
GET    /api/users/interpreters   - Get available interpreters
GET    /api/users/:userId        - Get user by ID
POST   /api/users/search         - Search users
DELETE /api/users/account        - Delete user account
```

### Room Management
```
POST   /api/rooms              - Create new room
GET    /api/rooms              - Get user's rooms
GET    /api/rooms/public       - Get public rooms
GET    /api/rooms/:roomId      - Get room details
POST   /api/rooms/:roomId/join - Join a room
POST   /api/rooms/:roomId/leave - Leave a room
PUT    /api/rooms/:roomId      - Update room settings
DELETE /api/rooms/:roomId      - Delete room
POST   /api/rooms/:roomId/invite - Generate invite link
```

### Call Management
```
POST   /api/calls                    - Initiate new call
GET    /api/calls                    - Get call history
GET    /api/calls/:callId            - Get call details
PUT    /api/calls/:callId/start      - Start call
PUT    /api/calls/:callId/end        - End call
POST   /api/calls/:callId/join       - Join call
POST   /api/calls/:callId/leave      - Leave call
POST   /api/calls/:callId/transcription - Add transcription
POST   /api/calls/:callId/rating     - Rate call quality
```

## 🔌 WebSocket Events

### Client to Server
```javascript
// Room management
socket.emit('join-room', { roomId, role })
socket.emit('leave-room', { roomId })

// WebRTC signaling
socket.emit('webrtc-offer', { roomId, targetUserId, offer })
socket.emit('webrtc-answer', { roomId, targetUserId, answer })
socket.emit('webrtc-ice-candidate', { roomId, targetUserId, candidate })

// Communication
socket.emit('send-message', { roomId, content, type })
socket.emit('gesture-detected', { roomId, gestureData, confidence, translation })

// Status updates
socket.emit('status-update', { status })
socket.emit('typing-start', { roomId })
socket.emit('typing-stop', { roomId })
```

### Server to Client
```javascript
// Room events
socket.on('room-joined', (data))
socket.on('user-joined', (data))
socket.on('user-left', (data))
socket.on('user-disconnected', (data))

// WebRTC events
socket.on('webrtc-offer', (data))
socket.on('webrtc-answer', (data))
socket.on('webrtc-ice-candidate', (data))

// Communication events
socket.on('new-message', (data))
socket.on('gesture-recognized', (data))
socket.on('user-typing', (data))

// Status events
socket.on('user-status-changed', (data))
socket.on('quality-update', (data))
```

## 🔧 Configuration

### Database Models
- **User**: User profiles, authentication, preferences
- **Room**: Video call rooms with settings and participants
- **Call**: Call sessions with participants and metrics
- **Message**: Chat messages and system notifications

### Security Features
- JWT authentication with refresh tokens
- Password hashing with bcrypt
- Rate limiting on API endpoints
- Input validation and sanitization
- CORS configuration
- Helmet security headers

### Email Templates
- Email verification
- Password reset
- Call invitations
- System notifications

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- --grep "auth"
```

## 📊 Monitoring & Logging

### Health Check
```bash
curl http://localhost:5000/health
```

### Logs
- Application logs: `./logs/app.log`
- Error logs: `./logs/error.log`
- Access logs: Combined with Morgan middleware

### Metrics
- Call duration and quality metrics
- User activity tracking
- System performance monitoring
- Error rate tracking

## 🚀 Production Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Configure production database
3. Set up SSL certificates
4. Configure email service
5. Set up monitoring and logging

### Performance Optimization
- Enable compression middleware
- Configure Redis for session management
- Set up CDN for static assets
- Implement database indexing
- Configure load balancing

### Security Checklist
- [ ] Update all default passwords
- [ ] Configure firewall rules
- [ ] Set up SSL/TLS certificates
- [ ] Enable rate limiting
- [ ] Configure CORS properly
- [ ] Set up monitoring and alerts

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the API examples

## 🔮 Future Enhancements

- [ ] ML model integration for real-time gesture recognition
- [ ] Advanced analytics dashboard
- [ ] Mobile app support
- [ ] Multi-language support
- [ ] Advanced call recording features
- [ ] Integration with external ML services
- [ ] Performance optimization
- [ ] Advanced security features

# 🤲 Sign Language Call Application

A professional real-time video calling application with advanced hand gesture recognition and sign language support.

## ✨ Features

- **🎥 Real-time Video Calling** - High-quality WebRTC video communication
- **🤲 Hand Gesture Recognition** - AI-powered gesture detection using Python + OpenCV
- **🎯 Professional UI/UX** - Beautiful, responsive interface with modern design
- **⚡ Real-time Processing** - Live gesture-to-text conversion
- **📊 Statistics Tracking** - Gesture count and accuracy metrics
- **🔄 Socket.IO Integration** - Real-time communication between frontend and backend

## 🚀 Technologies Used

### Frontend
- **HTML5/CSS3** - Modern responsive design
- **JavaScript ES6+** - Interactive user interface
- **Socket.IO Client** - Real-time communication
- **WebRTC** - Video calling capabilities

### Backend
- **Node.js** - Server runtime
- **Express.js** - Web framework
- **Socket.IO** - Real-time bidirectional communication
- **MongoDB + Mongoose** - Database and ODM

### AI/Computer Vision
- **Python 3.x** - Gesture recognition backend
- **OpenCV** - Computer vision processing
- **Flask + Flask-SocketIO** - Python web server
- **NumPy** - Numerical computations

## 📁 Project Structure

```
sign-language-call/
├── 📄 sign-language-call.html    # Main application interface
├── 📄 call-script.js             # Frontend JavaScript logic
├── 📄 call-styles.css            # UI styling
├── 🐍 working_gesture.py         # Python gesture recognition server
├── 🖥️ server.js                  # Main Node.js server
├── 📦 package.json               # Node.js dependencies
├── 📁 routes/                    # API route handlers
│   ├── auth.js                   # Authentication routes
│   ├── calls.js                  # Call management
│   ├── rooms.js                  # Room management
│   └── users.js                  # User management
├── 📁 models/                    # Database models
│   ├── Call.js                   # Call data model
│   ├── Room.js                   # Room data model
│   └── User.js                   # User data model
├── 📁 config/                    # Configuration files
│   └── database.js               # Database configuration
├── 📁 middleware/                # Express middleware
│   ├── auth.js                   # Authentication middleware
│   └── errorHandler.js           # Error handling
├── 📁 socket/                    # Socket.IO handlers
│   └── socketHandler.js          # Real-time communication
└── 📁 utils/                     # Utility functions
    └── sendEmail.js              # Email utilities
```

## 🛠️ Installation & Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd sign-language-call
```

### 2. Install Node.js Dependencies
```bash
npm install
```

### 3. Install Python Dependencies
```bash
pip install flask flask-socketio opencv-python numpy
```

### 4. Environment Setup
Create a `.env` file:
```env
PORT=5000
MONGODB_URI=mongodb://localhost:27017/signlanguage
JWT_SECRET=your_jwt_secret_here
```

### 5. Start the Application

**Terminal 1 - Node.js Server:**
```bash
npm start
# Server runs on http://localhost:5000
```

**Terminal 2 - Python Gesture Recognition:**
```bash
python working_gesture.py
# Gesture server runs on http://localhost:3000
```

## 🎮 Usage

### 1. **Access Main Interface**
- Open `http://localhost:5000/sign-language-call.html`
- Beautiful professional interface loads

### 2. **Start Video Call**
- Click "Start Camera" to enable video
- Allow camera permissions when prompted

### 3. **Enable Gesture Recognition**
- Click the gesture recognition button (🤲)
- System connects to Python backend automatically

### 4. **Make Gestures**
- Show your hand clearly to the camera
- Supported gestures:
  - ✊ **Closed Fist** → "Stop"
  - ☝️ **One Finger** → "1"
  - ✌️ **Two Fingers** → "2"
  - 🖐️ **Open Hand** → "Hello"
  - 👍 **Thumbs Up** → "Good"

### 5. **View Results**
- Real-time gesture detection appears in translation area
- Statistics show gesture count and accuracy
- Visual feedback with confidence percentages

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/verify` - Token verification

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update profile

### Rooms
- `POST /api/rooms/create` - Create new room
- `GET /api/rooms/:id` - Get room details
- `POST /api/rooms/:id/join` - Join room

### Calls
- `POST /api/calls/start` - Start new call
- `GET /api/calls/:id` - Get call details
- `POST /api/calls/:id/end` - End call

## 🎯 Gesture Recognition Features

### Supported Gestures
| Gesture | Output | Confidence |
|---------|--------|------------|
| ✊ Closed Fist | "Stop" | 85-90% |
| ☝️ One Finger | "1" | 85-90% |
| ✌️ Two Fingers | "2" | 85-90% |
| 🖐️ Open Hand | "Hello" | 85-90% |
| 👍 Thumbs Up | "Good" | 80-85% |

### Technical Details
- **Frame Rate**: 3-4 FPS processing
- **Detection Method**: Contour analysis + convexity defects
- **Color Spaces**: HSV + YCrCb for better skin detection
- **Noise Reduction**: Gaussian blur + morphological operations

## 💡 Tips for Best Results

### Lighting
- Use bright, even lighting
- Avoid shadows on your hand
- Natural daylight works best

### Background
- Use plain, contrasting background
- Avoid busy patterns behind your hand

### Hand Position
- Keep hand 1-2 feet from camera
- Make clear, distinct gestures
- Hold gestures for 2-3 seconds

### Camera Quality
- Higher resolution cameras work better
- Ensure camera is stable and focused

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenCV community for computer vision tools
- Socket.IO for real-time communication
- WebRTC for video calling capabilities
- Flask community for Python web framework

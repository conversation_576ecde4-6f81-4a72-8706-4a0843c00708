{"name": "sign-language-call-backend", "version": "1.0.0", "description": "Backend server for sign language video call application", "main": "server.js", "scripts": {"start": "node server.js", "demo": "node server-demo.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["sign-language", "video-call", "webrtc", "real-time", "accessibility"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "uuid": "^9.0.0", "moment": "^2.29.4", "nodemailer": "^6.9.4", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}
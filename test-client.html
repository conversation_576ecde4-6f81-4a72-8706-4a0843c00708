<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Language Call - Test Client</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e74c3c;
        }
        .status-dot.connected {
            background: #27ae60;
        }
        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .control-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
        }
        .control-group h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
        }
        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .btn.danger {
            background: #e74c3c;
        }
        .btn.danger:hover {
            background: #c0392b;
        }
        .logs {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 12px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.info {
            color: #3498db;
        }
        .log-entry.success {
            color: #27ae60;
        }
        .log-entry.error {
            color: #e74c3c;
        }
        .log-entry.warning {
            color: #f39c12;
        }
        .timestamp {
            color: #95a5a6;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤝 Sign Language Call - Test Client</h1>
            <div class="status">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Disconnected</span>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <h3>🔌 Connection</h3>
                <button class="btn" id="connectBtn">Connect to Server</button>
                <button class="btn danger" id="disconnectBtn" disabled>Disconnect</button>
            </div>

            <div class="control-group">
                <h3>🏠 Room Management</h3>
                <div class="input-group">
                    <label>Room ID:</label>
                    <input type="text" id="roomIdInput" placeholder="Enter room ID" value="test-room-123">
                </div>
                <div class="input-group">
                    <label>Your Name:</label>
                    <input type="text" id="userNameInput" placeholder="Enter your name" value="Test User">
                </div>
                <button class="btn" id="joinRoomBtn" disabled>Join Room</button>
                <button class="btn danger" id="leaveRoomBtn" disabled>Leave Room</button>
            </div>

            <div class="control-group">
                <h3>💬 Messaging</h3>
                <div class="input-group">
                    <label>Message:</label>
                    <input type="text" id="messageInput" placeholder="Type a message">
                </div>
                <button class="btn" id="sendMessageBtn" disabled>Send Message</button>
            </div>

            <div class="control-group">
                <h3>👋 Gesture Simulation</h3>
                <div class="input-group">
                    <label>Gesture Translation:</label>
                    <input type="text" id="gestureInput" placeholder="Hello, how are you?" value="Hello, how are you?">
                </div>
                <div class="input-group">
                    <label>Confidence (0-1):</label>
                    <input type="number" id="confidenceInput" min="0" max="1" step="0.1" value="0.95">
                </div>
                <button class="btn" id="sendGestureBtn" disabled>Send Gesture</button>
            </div>
        </div>

        <div class="control-group">
            <h3>📋 Event Logs</h3>
            <div class="logs" id="logs"></div>
            <button class="btn" id="clearLogsBtn" style="margin-top: 10px;">Clear Logs</button>
        </div>
    </div>

    <script>
        let socket = null;
        let isConnected = false;
        let currentRoom = null;

        // DOM elements
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const joinRoomBtn = document.getElementById('joinRoomBtn');
        const leaveRoomBtn = document.getElementById('leaveRoomBtn');
        const sendMessageBtn = document.getElementById('sendMessageBtn');
        const sendGestureBtn = document.getElementById('sendGestureBtn');
        const clearLogsBtn = document.getElementById('clearLogsBtn');
        const logs = document.getElementById('logs');

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span>${message}`;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }

        // Update UI state
        function updateUI() {
            statusDot.className = `status-dot ${isConnected ? 'connected' : ''}`;
            statusText.textContent = isConnected ? 'Connected' : 'Disconnected';
            
            connectBtn.disabled = isConnected;
            disconnectBtn.disabled = !isConnected;
            joinRoomBtn.disabled = !isConnected;
            leaveRoomBtn.disabled = !isConnected || !currentRoom;
            sendMessageBtn.disabled = !isConnected || !currentRoom;
            sendGestureBtn.disabled = !isConnected || !currentRoom;
        }

        // Connect to server
        function connectToServer() {
            try {
                socket = io('http://localhost:5000');
                
                socket.on('connect', () => {
                    isConnected = true;
                    updateUI();
                    log('✅ Connected to server', 'success');
                });

                socket.on('disconnect', (reason) => {
                    isConnected = false;
                    currentRoom = null;
                    updateUI();
                    log(`❌ Disconnected: ${reason}`, 'error');
                });

                socket.on('room-joined', (data) => {
                    currentRoom = data.roomId;
                    updateUI();
                    log(`🏠 Joined room: ${data.roomId}`, 'success');
                });

                socket.on('user-joined', (data) => {
                    log(`👤 User joined: ${data.userName || data.socketId}`, 'info');
                });

                socket.on('user-left', (data) => {
                    log(`👤 User left: ${data.socketId}`, 'warning');
                });

                socket.on('user-disconnected', (data) => {
                    log(`👤 User disconnected: ${data.socketId}`, 'warning');
                });

                socket.on('new-message', (data) => {
                    log(`💬 ${data.userName}: ${data.message}`, 'info');
                });

                socket.on('gesture-recognized', (data) => {
                    log(`👋 Gesture: "${data.translation}" (${Math.round(data.confidence * 100)}%)`, 'success');
                });

                socket.on('webrtc-offer', (data) => {
                    log(`📞 WebRTC offer from: ${data.fromSocketId}`, 'info');
                });

                socket.on('webrtc-answer', (data) => {
                    log(`📞 WebRTC answer from: ${data.fromSocketId}`, 'info');
                });

                socket.on('webrtc-ice-candidate', (data) => {
                    log(`📞 ICE candidate from: ${data.fromSocketId}`, 'info');
                });

                socket.on('error', (data) => {
                    log(`❌ Error: ${data.message}`, 'error');
                });

            } catch (error) {
                log(`❌ Connection failed: ${error.message}`, 'error');
            }
        }

        // Event listeners
        connectBtn.addEventListener('click', connectToServer);

        disconnectBtn.addEventListener('click', () => {
            if (socket) {
                socket.disconnect();
            }
        });

        joinRoomBtn.addEventListener('click', () => {
            const roomId = document.getElementById('roomIdInput').value.trim();
            const userName = document.getElementById('userNameInput').value.trim();
            
            if (!roomId) {
                log('❌ Please enter a room ID', 'error');
                return;
            }

            socket.emit('join-room', { roomId, userName });
            log(`🔄 Joining room: ${roomId}`, 'info');
        });

        leaveRoomBtn.addEventListener('click', () => {
            if (currentRoom) {
                socket.emit('leave-room', { roomId: currentRoom });
                log(`🔄 Leaving room: ${currentRoom}`, 'info');
                currentRoom = null;
                updateUI();
            }
        });

        sendMessageBtn.addEventListener('click', () => {
            const message = document.getElementById('messageInput').value.trim();
            const userName = document.getElementById('userNameInput').value.trim();
            
            if (!message) {
                log('❌ Please enter a message', 'error');
                return;
            }

            socket.emit('send-message', { 
                roomId: currentRoom, 
                message, 
                userName 
            });
            
            document.getElementById('messageInput').value = '';
            log(`📤 Sent message: ${message}`, 'info');
        });

        sendGestureBtn.addEventListener('click', () => {
            const translation = document.getElementById('gestureInput').value.trim();
            const confidence = parseFloat(document.getElementById('confidenceInput').value);
            
            if (!translation) {
                log('❌ Please enter a gesture translation', 'error');
                return;
            }

            socket.emit('gesture-detected', {
                roomId: currentRoom,
                gestureData: { /* simulated gesture data */ },
                translation,
                confidence
            });
            
            log(`📤 Sent gesture: "${translation}" (${Math.round(confidence * 100)}%)`, 'info');
        });

        clearLogsBtn.addEventListener('click', () => {
            logs.innerHTML = '';
        });

        // Enter key support for inputs
        document.getElementById('messageInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !sendMessageBtn.disabled) {
                sendMessageBtn.click();
            }
        });

        // Initialize UI
        updateUI();
        log('🚀 Test client initialized', 'info');
        log('💡 Click "Connect to Server" to start testing', 'info');
    </script>
</body>
</html>

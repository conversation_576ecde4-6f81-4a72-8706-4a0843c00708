const express = require('express');
const { body, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');
const Room = require('../models/Room');
const { asyncHandler, handleValidationError } = require('../middleware/errorHandler');
const { authorize, requireVerification } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createRoomValidation = [
    body('name')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Room name must be between 1 and 100 characters'),
    body('type')
        .isIn(['public', 'private', 'scheduled'])
        .withMessage('Room type must be public, private, or scheduled'),
    body('settings.maxParticipants')
        .optional()
        .isInt({ min: 2, max: 50 })
        .withMessage('Max participants must be between 2 and 50'),
    body('settings.languages')
        .optional()
        .isArray()
        .withMessage('Languages must be an array')
];

// @route   POST /api/rooms
// @desc    Create a new room
// @access  Private
router.post('/', createRoomValidation, asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json(handleValidationError(errors));
    }

    const { name, description, type, settings, schedule } = req.body;

    // Create room
    const room = await Room.create({
        roomId: uuidv4(),
        name,
        description,
        type,
        host: req.user._id,
        settings: {
            maxParticipants: settings?.maxParticipants || 10,
            requirePassword: settings?.requirePassword || false,
            password: settings?.password,
            allowRecording: settings?.allowRecording || false,
            autoTranscription: settings?.autoTranscription || true,
            languages: settings?.languages || [],
            videoQuality: settings?.videoQuality || 'auto'
        },
        schedule: schedule || {}
    });

    // Add host as participant
    await room.addParticipant(req.user._id, 'host');

    // Populate room data
    const populatedRoom = await Room.findById(room._id)
        .populate('host', 'name profile.avatar')
        .populate('participants.user', 'name profile.avatar');

    res.status(201).json({
        success: true,
        message: 'Room created successfully',
        data: {
            room: populatedRoom
        }
    });
}));

// @route   GET /api/rooms
// @desc    Get user's rooms
// @access  Private
router.get('/', asyncHandler(async (req, res) => {
    const { type, status, page = 1, limit = 10 } = req.query;

    // Build query
    const query = {
        $or: [
            { host: req.user._id },
            { 'participants.user': req.user._id }
        ]
    };

    if (type) {
        query.type = type;
    }

    if (status) {
        query.status = status;
    }

    // Pagination
    const skip = (page - 1) * limit;

    const rooms = await Room.find(query)
        .populate('host', 'name profile.avatar')
        .populate('participants.user', 'name profile.avatar')
        .sort({ updatedAt: -1 })
        .skip(skip)
        .limit(parseInt(limit));

    const total = await Room.countDocuments(query);

    res.json({
        success: true,
        data: {
            rooms,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));

// @route   GET /api/rooms/public
// @desc    Get public rooms
// @access  Private
router.get('/public', asyncHandler(async (req, res) => {
    const { languages, page = 1, limit = 10 } = req.query;

    const languageFilter = languages ? languages.split(',') : [];
    
    const rooms = await Room.findPublicRooms(languageFilter);

    // Apply pagination
    const skip = (page - 1) * limit;
    const paginatedRooms = rooms.slice(skip, skip + parseInt(limit));

    res.json({
        success: true,
        data: {
            rooms: paginatedRooms,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: rooms.length,
                pages: Math.ceil(rooms.length / limit)
            }
        }
    });
}));

// @route   GET /api/rooms/:roomId
// @desc    Get room details
// @access  Private
router.get('/:roomId', asyncHandler(async (req, res) => {
    const { roomId } = req.params;

    const room = await Room.findOne({ roomId })
        .populate('host', 'name profile.avatar')
        .populate('participants.user', 'name profile.avatar')
        .populate('messages.sender', 'name profile.avatar');

    if (!room) {
        return res.status(404).json({
            success: false,
            message: 'Room not found'
        });
    }

    // Check if user has access to room
    const isParticipant = room.participants.some(
        p => p.user._id.toString() === req.user._id.toString()
    );
    const isHost = room.host._id.toString() === req.user._id.toString();

    if (room.type === 'private' && !isParticipant && !isHost) {
        return res.status(403).json({
            success: false,
            message: 'Access denied to private room'
        });
    }

    res.json({
        success: true,
        data: {
            room
        }
    });
}));

// @route   POST /api/rooms/:roomId/join
// @desc    Join a room
// @access  Private
router.post('/:roomId/join', asyncHandler(async (req, res) => {
    const { roomId } = req.params;
    const { password, inviteToken } = req.body;

    const room = await Room.findOne({ roomId });

    if (!room) {
        return res.status(404).json({
            success: false,
            message: 'Room not found'
        });
    }

    // Check if room requires password
    if (room.settings.requirePassword && !password && !inviteToken) {
        return res.status(401).json({
            success: false,
            message: 'Password required to join this room'
        });
    }

    // Validate password if provided
    if (room.settings.requirePassword && password) {
        const bcrypt = require('bcryptjs');
        const isPasswordValid = await bcrypt.compare(password, room.settings.password);
        
        if (!isPasswordValid) {
            return res.status(401).json({
                success: false,
                message: 'Invalid room password'
            });
        }
    }

    // Validate invite token if provided
    if (inviteToken) {
        try {
            room.validateInviteLink(inviteToken);
            await room.useInviteLink(inviteToken);
        } catch (error) {
            return res.status(401).json({
                success: false,
                message: error.message
            });
        }
    }

    // Add user to room
    try {
        await room.addParticipant(req.user._id);

        const updatedRoom = await Room.findOne({ roomId })
            .populate('host', 'name profile.avatar')
            .populate('participants.user', 'name profile.avatar');

        res.json({
            success: true,
            message: 'Successfully joined room',
            data: {
                room: updatedRoom
            }
        });
    } catch (error) {
        return res.status(400).json({
            success: false,
            message: error.message
        });
    }
}));

// @route   POST /api/rooms/:roomId/leave
// @desc    Leave a room
// @access  Private
router.post('/:roomId/leave', asyncHandler(async (req, res) => {
    const { roomId } = req.params;

    const room = await Room.findOne({ roomId });

    if (!room) {
        return res.status(404).json({
            success: false,
            message: 'Room not found'
        });
    }

    // Remove user from room
    await room.removeParticipant(req.user._id);

    res.json({
        success: true,
        message: 'Successfully left room'
    });
}));

// @route   PUT /api/rooms/:roomId
// @desc    Update room settings
// @access  Private (Host only)
router.put('/:roomId', asyncHandler(async (req, res) => {
    const { roomId } = req.params;
    const { name, description, settings } = req.body;

    const room = await Room.findOne({ roomId });

    if (!room) {
        return res.status(404).json({
            success: false,
            message: 'Room not found'
        });
    }

    // Check if user is host
    if (room.host.toString() !== req.user._id.toString()) {
        return res.status(403).json({
            success: false,
            message: 'Only room host can update settings'
        });
    }

    // Update room
    if (name) room.name = name;
    if (description) room.description = description;
    if (settings) {
        room.settings = { ...room.settings, ...settings };
    }

    await room.save();

    const updatedRoom = await Room.findOne({ roomId })
        .populate('host', 'name profile.avatar')
        .populate('participants.user', 'name profile.avatar');

    res.json({
        success: true,
        message: 'Room updated successfully',
        data: {
            room: updatedRoom
        }
    });
}));

// @route   DELETE /api/rooms/:roomId
// @desc    Delete a room
// @access  Private (Host only)
router.delete('/:roomId', asyncHandler(async (req, res) => {
    const { roomId } = req.params;

    const room = await Room.findOne({ roomId });

    if (!room) {
        return res.status(404).json({
            success: false,
            message: 'Room not found'
        });
    }

    // Check if user is host
    if (room.host.toString() !== req.user._id.toString()) {
        return res.status(403).json({
            success: false,
            message: 'Only room host can delete room'
        });
    }

    // End room if active
    if (room.status === 'active') {
        await room.endCall();
    }

    await Room.findByIdAndDelete(room._id);

    res.json({
        success: true,
        message: 'Room deleted successfully'
    });
}));

// @route   POST /api/rooms/:roomId/invite
// @desc    Generate invite link
// @access  Private (Host only)
router.post('/:roomId/invite', asyncHandler(async (req, res) => {
    const { roomId } = req.params;
    const { expiresIn = 24, usageLimit = 1 } = req.body;

    const room = await Room.findOne({ roomId });

    if (!room) {
        return res.status(404).json({
            success: false,
            message: 'Room not found'
        });
    }

    // Check if user is host or participant
    const isParticipant = room.participants.some(
        p => p.user.toString() === req.user._id.toString()
    );
    const isHost = room.host.toString() === req.user._id.toString();

    if (!isHost && !isParticipant) {
        return res.status(403).json({
            success: false,
            message: 'Access denied'
        });
    }

    // Generate invite link
    await room.generateInviteLink(req.user._id, expiresIn, usageLimit);

    const latestInvite = room.inviteLinks[room.inviteLinks.length - 1];
    const inviteUrl = `${process.env.CLIENT_URL}/join/${roomId}?token=${latestInvite.token}`;

    res.json({
        success: true,
        message: 'Invite link generated successfully',
        data: {
            inviteUrl,
            token: latestInvite.token,
            expiresAt: latestInvite.expiresAt,
            usageLimit: latestInvite.usageLimit
        }
    });
}));

module.exports = router;

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');

const app = express();
const server = http.createServer(app);

// CORS configuration for Socket.IO
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"],
        credentials: true
    }
});

// In-memory storage for demo
const users = new Map();
const rooms = new Map();
const calls = new Map();
const activeConnections = new Map();

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "wss:", "ws:"],
            mediaSrc: ["'self'", "blob:"]
        }
    }
}));

// Middleware
app.use(compression());
app.use(morgan('combined'));
app.use(cors({
    origin: "*",
    credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static('public'));
app.use(express.static('.'));

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        message: 'Sign Language Call Backend is running!'
    });
});

// Demo API endpoints
app.get('/api/status', (req, res) => {
    res.json({
        success: true,
        data: {
            users: users.size,
            rooms: rooms.size,
            calls: calls.size,
            activeConnections: activeConnections.size
        }
    });
});

app.post('/api/demo/user', (req, res) => {
    const { name, email, role = 'sign_user' } = req.body;
    
    if (!name || !email) {
        return res.status(400).json({
            success: false,
            message: 'Name and email are required'
        });
    }
    
    const userId = Date.now().toString();
    const user = {
        id: userId,
        name,
        email,
        role,
        status: 'online',
        createdAt: new Date()
    };
    
    users.set(userId, user);
    
    res.json({
        success: true,
        message: 'Demo user created successfully',
        data: { user }
    });
});

app.post('/api/demo/room', (req, res) => {
    const { name, type = 'public', maxParticipants = 10 } = req.body;
    
    if (!name) {
        return res.status(400).json({
            success: false,
            message: 'Room name is required'
        });
    }
    
    const roomId = `room_${Date.now()}`;
    const room = {
        roomId,
        name,
        type,
        maxParticipants,
        participants: [],
        status: 'waiting',
        createdAt: new Date()
    };
    
    rooms.set(roomId, room);
    
    res.json({
        success: true,
        message: 'Demo room created successfully',
        data: { room }
    });
});

app.get('/api/demo/rooms', (req, res) => {
    const roomList = Array.from(rooms.values());
    
    res.json({
        success: true,
        data: {
            rooms: roomList,
            total: roomList.length
        }
    });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log(`🔌 User connected: ${socket.id}`);
    
    // Store connection
    activeConnections.set(socket.id, {
        socketId: socket.id,
        connectedAt: new Date()
    });
    
    // Handle joining a room
    socket.on('join-room', (data) => {
        try {
            const { roomId, userName = 'Anonymous User' } = data;
            
            console.log(`👤 ${userName} joining room: ${roomId}`);
            
            // Join socket room
            socket.join(roomId);
            
            // Update room participants
            if (rooms.has(roomId)) {
                const room = rooms.get(roomId);
                room.participants.push({
                    socketId: socket.id,
                    userName,
                    joinedAt: new Date()
                });
                rooms.set(roomId, room);
            }
            
            // Notify others in the room
            socket.to(roomId).emit('user-joined', {
                socketId: socket.id,
                userName,
                timestamp: new Date()
            });
            
            // Send confirmation to user
            socket.emit('room-joined', {
                roomId,
                message: `Successfully joined room: ${roomId}`,
                timestamp: new Date()
            });
            
        } catch (error) {
            console.error('Join room error:', error);
            socket.emit('error', { message: 'Failed to join room' });
        }
    });
    
    // Handle leaving a room
    socket.on('leave-room', (data) => {
        try {
            const { roomId } = data;
            
            console.log(`👤 User leaving room: ${roomId}`);
            
            // Leave socket room
            socket.leave(roomId);
            
            // Update room participants
            if (rooms.has(roomId)) {
                const room = rooms.get(roomId);
                room.participants = room.participants.filter(p => p.socketId !== socket.id);
                rooms.set(roomId, room);
            }
            
            // Notify others in the room
            socket.to(roomId).emit('user-left', {
                socketId: socket.id,
                timestamp: new Date()
            });
            
        } catch (error) {
            console.error('Leave room error:', error);
            socket.emit('error', { message: 'Failed to leave room' });
        }
    });
    
    // Handle WebRTC signaling
    socket.on('webrtc-offer', (data) => {
        const { roomId, targetSocketId, offer } = data;
        console.log(`📞 WebRTC offer in room ${roomId}`);
        
        if (targetSocketId) {
            io.to(targetSocketId).emit('webrtc-offer', {
                fromSocketId: socket.id,
                offer: offer
            });
        } else {
            socket.to(roomId).emit('webrtc-offer', {
                fromSocketId: socket.id,
                offer: offer
            });
        }
    });
    
    socket.on('webrtc-answer', (data) => {
        const { roomId, targetSocketId, answer } = data;
        console.log(`📞 WebRTC answer in room ${roomId}`);
        
        if (targetSocketId) {
            io.to(targetSocketId).emit('webrtc-answer', {
                fromSocketId: socket.id,
                answer: answer
            });
        }
    });
    
    socket.on('webrtc-ice-candidate', (data) => {
        const { roomId, targetSocketId, candidate } = data;
        
        if (targetSocketId) {
            io.to(targetSocketId).emit('webrtc-ice-candidate', {
                fromSocketId: socket.id,
                candidate: candidate
            });
        } else {
            socket.to(roomId).emit('webrtc-ice-candidate', {
                fromSocketId: socket.id,
                candidate: candidate
            });
        }
    });
    
    // Handle chat messages
    socket.on('send-message', (data) => {
        try {
            const { roomId, message, userName = 'Anonymous' } = data;
            
            console.log(`💬 Message in room ${roomId}: ${message}`);
            
            // Broadcast message to room
            io.to(roomId).emit('new-message', {
                socketId: socket.id,
                userName,
                message,
                timestamp: new Date()
            });
            
        } catch (error) {
            console.error('Send message error:', error);
            socket.emit('error', { message: 'Failed to send message' });
        }
    });
    
    // Handle gesture detection
    socket.on('gesture-detected', (data) => {
        try {
            const { roomId, gestureData, translation, confidence } = data;
            
            console.log(`👋 Gesture detected in room ${roomId}: ${translation}`);
            
            // Broadcast gesture to room
            socket.to(roomId).emit('gesture-recognized', {
                socketId: socket.id,
                gestureData,
                translation,
                confidence,
                timestamp: new Date()
            });
            
        } catch (error) {
            console.error('Gesture detection error:', error);
            socket.emit('error', { message: 'Failed to process gesture' });
        }
    });
    
    // Handle disconnection
    socket.on('disconnect', (reason) => {
        console.log(`🔌 User disconnected: ${socket.id} (${reason})`);
        
        // Remove from active connections
        activeConnections.delete(socket.id);
        
        // Remove from all rooms
        for (const [roomId, room] of rooms.entries()) {
            room.participants = room.participants.filter(p => p.socketId !== socket.id);
            rooms.set(roomId, room);
            
            // Notify room about disconnection
            socket.to(roomId).emit('user-disconnected', {
                socketId: socket.id,
                timestamp: new Date()
            });
        }
    });
    
    // Handle errors
    socket.on('error', (error) => {
        console.error('Socket error:', error);
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route not found'
    });
});

const PORT = process.env.PORT || 5000;

server.listen(PORT, () => {
    console.log(`🚀 Sign Language Call Backend Demo running on port ${PORT}`);
    console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🌐 Health check: http://localhost:${PORT}/health`);
    console.log(`📊 Status: http://localhost:${PORT}/api/status`);
    console.log(`📁 Static files served from current directory`);
    console.log(`\n🎯 Ready for sign language video calls!`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    server.close(() => {
        console.log('Process terminated');
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    server.close(() => {
        console.log('Process terminated');
    });
});

module.exports = { app, server, io };

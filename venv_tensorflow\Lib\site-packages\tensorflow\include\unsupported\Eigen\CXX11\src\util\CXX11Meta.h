// This file is part of Eigen, a lightweight C++ template library
// for linear algebra.
//
// Copyright (C) 2013 <PERSON> <<EMAIL>>
//
// This Source Code Form is subject to the terms of the Mozilla
// Public License v. 2.0. If a copy of the MPL was not distributed
// with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

#ifndef EIGEN_CXX11META_H
#define EIGEN_CXX11META_H

#include <vector>
#include "../../../../../Eigen/src/Core/util/EmulateArray.h"

#include "CXX11Workarounds.h"

#endif  // EIGEN_CXX11META_H

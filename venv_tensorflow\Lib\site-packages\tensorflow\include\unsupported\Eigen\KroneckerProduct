// This file is part of Eigen, a lightweight C++ template library
// for linear algebra.
//
//
// This Source Code Form is subject to the terms of the Mozilla
// Public License v. 2.0. If a copy of the MPL was not distributed
// with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

#ifndef EIGEN_KRONECKER_PRODUCT_MODULE_H
#define EIGEN_KRONECKER_PRODUCT_MODULE_H

#include "../../Eigen/Core"
#include "../../Eigen/SparseCore"

namespace Eigen {

/**
 * \defgroup KroneckerProduct_Module KroneckerProduct module
 *
 * This module contains an experimental Kronecker product implementation.
 *
 * \code
 * #include <Eigen/KroneckerProduct>
 * \endcode
 */

}  // namespace Eigen

// IWYU pragma: begin_exports
#include "src/KroneckerProduct/KroneckerTensorProduct.h"
// IWYU pragma: end_exports

#include "../../Eigen/src/Core/util/ReenableStupidWarnings.h"

#endif  // EIG<PERSON>_KRONECKER_PRODUCT_MODULE_H

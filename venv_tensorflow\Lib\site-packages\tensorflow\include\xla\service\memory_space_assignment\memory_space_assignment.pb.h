// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/service/memory_space_assignment/memory_space_assignment.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
namespace xla {
namespace memory_space_assignment {
class HloOperandFilter;
struct HloOperandFilterDefaultTypeInternal;
extern HloOperandFilterDefaultTypeInternal _HloOperandFilter_default_instance_;
class HloPositionMatcher;
struct HloPositionMatcherDefaultTypeInternal;
extern HloPositionMatcherDefaultTypeInternal _HloPositionMatcher_default_instance_;
class MemoryBoundLoopOptimizerOptions;
struct MemoryBoundLoopOptimizerOptionsDefaultTypeInternal;
extern MemoryBoundLoopOptimizerOptionsDefaultTypeInternal _MemoryBoundLoopOptimizerOptions_default_instance_;
class MsaSortOrderOverride;
struct MsaSortOrderOverrideDefaultTypeInternal;
extern MsaSortOrderOverrideDefaultTypeInternal _MsaSortOrderOverride_default_instance_;
class MsaSortOrderOverrideOptions;
struct MsaSortOrderOverrideOptionsDefaultTypeInternal;
extern MsaSortOrderOverrideOptionsDefaultTypeInternal _MsaSortOrderOverrideOptions_default_instance_;
class MsaSortOrderOverrides;
struct MsaSortOrderOverridesDefaultTypeInternal;
extern MsaSortOrderOverridesDefaultTypeInternal _MsaSortOrderOverrides_default_instance_;
class PreferredPrefetchOverride;
struct PreferredPrefetchOverrideDefaultTypeInternal;
extern PreferredPrefetchOverrideDefaultTypeInternal _PreferredPrefetchOverride_default_instance_;
class PreferredPrefetchOverrideOptions;
struct PreferredPrefetchOverrideOptionsDefaultTypeInternal;
extern PreferredPrefetchOverrideOptionsDefaultTypeInternal _PreferredPrefetchOverrideOptions_default_instance_;
class PreferredPrefetchOverrides;
struct PreferredPrefetchOverridesDefaultTypeInternal;
extern PreferredPrefetchOverridesDefaultTypeInternal _PreferredPrefetchOverrides_default_instance_;
class SlicedPrefetchOptions;
struct SlicedPrefetchOptionsDefaultTypeInternal;
extern SlicedPrefetchOptionsDefaultTypeInternal _SlicedPrefetchOptions_default_instance_;
class TupleShapeIndex;
struct TupleShapeIndexDefaultTypeInternal;
extern TupleShapeIndexDefaultTypeInternal _TupleShapeIndex_default_instance_;
class WindowPrefetchDetail;
struct WindowPrefetchDetailDefaultTypeInternal;
extern WindowPrefetchDetailDefaultTypeInternal _WindowPrefetchDetail_default_instance_;
class WindowPrefetchDetail_WindowDetail;
struct WindowPrefetchDetail_WindowDetailDefaultTypeInternal;
extern WindowPrefetchDetail_WindowDetailDefaultTypeInternal _WindowPrefetchDetail_WindowDetail_default_instance_;
}  // namespace memory_space_assignment
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::memory_space_assignment::HloOperandFilter* Arena::CreateMaybeMessage<::xla::memory_space_assignment::HloOperandFilter>(Arena*);
template<> ::xla::memory_space_assignment::HloPositionMatcher* Arena::CreateMaybeMessage<::xla::memory_space_assignment::HloPositionMatcher>(Arena*);
template<> ::xla::memory_space_assignment::MemoryBoundLoopOptimizerOptions* Arena::CreateMaybeMessage<::xla::memory_space_assignment::MemoryBoundLoopOptimizerOptions>(Arena*);
template<> ::xla::memory_space_assignment::MsaSortOrderOverride* Arena::CreateMaybeMessage<::xla::memory_space_assignment::MsaSortOrderOverride>(Arena*);
template<> ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* Arena::CreateMaybeMessage<::xla::memory_space_assignment::MsaSortOrderOverrideOptions>(Arena*);
template<> ::xla::memory_space_assignment::MsaSortOrderOverrides* Arena::CreateMaybeMessage<::xla::memory_space_assignment::MsaSortOrderOverrides>(Arena*);
template<> ::xla::memory_space_assignment::PreferredPrefetchOverride* Arena::CreateMaybeMessage<::xla::memory_space_assignment::PreferredPrefetchOverride>(Arena*);
template<> ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* Arena::CreateMaybeMessage<::xla::memory_space_assignment::PreferredPrefetchOverrideOptions>(Arena*);
template<> ::xla::memory_space_assignment::PreferredPrefetchOverrides* Arena::CreateMaybeMessage<::xla::memory_space_assignment::PreferredPrefetchOverrides>(Arena*);
template<> ::xla::memory_space_assignment::SlicedPrefetchOptions* Arena::CreateMaybeMessage<::xla::memory_space_assignment::SlicedPrefetchOptions>(Arena*);
template<> ::xla::memory_space_assignment::TupleShapeIndex* Arena::CreateMaybeMessage<::xla::memory_space_assignment::TupleShapeIndex>(Arena*);
template<> ::xla::memory_space_assignment::WindowPrefetchDetail* Arena::CreateMaybeMessage<::xla::memory_space_assignment::WindowPrefetchDetail>(Arena*);
template<> ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail* Arena::CreateMaybeMessage<::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {
namespace memory_space_assignment {

// ===================================================================

class SlicedPrefetchOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.SlicedPrefetchOptions) */ {
 public:
  inline SlicedPrefetchOptions() : SlicedPrefetchOptions(nullptr) {}
  ~SlicedPrefetchOptions() override;
  explicit PROTOBUF_CONSTEXPR SlicedPrefetchOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SlicedPrefetchOptions(const SlicedPrefetchOptions& from);
  SlicedPrefetchOptions(SlicedPrefetchOptions&& from) noexcept
    : SlicedPrefetchOptions() {
    *this = ::std::move(from);
  }

  inline SlicedPrefetchOptions& operator=(const SlicedPrefetchOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline SlicedPrefetchOptions& operator=(SlicedPrefetchOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SlicedPrefetchOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const SlicedPrefetchOptions* internal_default_instance() {
    return reinterpret_cast<const SlicedPrefetchOptions*>(
               &_SlicedPrefetchOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SlicedPrefetchOptions& a, SlicedPrefetchOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(SlicedPrefetchOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SlicedPrefetchOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SlicedPrefetchOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SlicedPrefetchOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SlicedPrefetchOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SlicedPrefetchOptions& from) {
    SlicedPrefetchOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SlicedPrefetchOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.SlicedPrefetchOptions";
  }
  protected:
  explicit SlicedPrefetchOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMinBytesFieldNumber = 2,
    kMaxSlicesFieldNumber = 1,
    kFailOnNonAlignmentBoundarySliceProposalFieldNumber = 3,
    kPreferredSliceSizeFieldNumber = 5,
    kAllSliceTimePermutationsThresholdFieldNumber = 4,
  };
  // uint64 min_bytes = 2;
  void clear_min_bytes();
  uint64_t min_bytes() const;
  void set_min_bytes(uint64_t value);
  private:
  uint64_t _internal_min_bytes() const;
  void _internal_set_min_bytes(uint64_t value);
  public:

  // uint32 max_slices = 1;
  void clear_max_slices();
  uint32_t max_slices() const;
  void set_max_slices(uint32_t value);
  private:
  uint32_t _internal_max_slices() const;
  void _internal_set_max_slices(uint32_t value);
  public:

  // bool fail_on_non_alignment_boundary_slice_proposal = 3;
  void clear_fail_on_non_alignment_boundary_slice_proposal();
  bool fail_on_non_alignment_boundary_slice_proposal() const;
  void set_fail_on_non_alignment_boundary_slice_proposal(bool value);
  private:
  bool _internal_fail_on_non_alignment_boundary_slice_proposal() const;
  void _internal_set_fail_on_non_alignment_boundary_slice_proposal(bool value);
  public:

  // uint64 preferred_slice_size = 5;
  void clear_preferred_slice_size();
  uint64_t preferred_slice_size() const;
  void set_preferred_slice_size(uint64_t value);
  private:
  uint64_t _internal_preferred_slice_size() const;
  void _internal_set_preferred_slice_size(uint64_t value);
  public:

  // uint32 all_slice_time_permutations_threshold = 4;
  void clear_all_slice_time_permutations_threshold();
  uint32_t all_slice_time_permutations_threshold() const;
  void set_all_slice_time_permutations_threshold(uint32_t value);
  private:
  uint32_t _internal_all_slice_time_permutations_threshold() const;
  void _internal_set_all_slice_time_permutations_threshold(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.SlicedPrefetchOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t min_bytes_;
    uint32_t max_slices_;
    bool fail_on_non_alignment_boundary_slice_proposal_;
    uint64_t preferred_slice_size_;
    uint32_t all_slice_time_permutations_threshold_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class WindowPrefetchDetail_WindowDetail final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.WindowPrefetchDetail.WindowDetail) */ {
 public:
  inline WindowPrefetchDetail_WindowDetail() : WindowPrefetchDetail_WindowDetail(nullptr) {}
  ~WindowPrefetchDetail_WindowDetail() override;
  explicit PROTOBUF_CONSTEXPR WindowPrefetchDetail_WindowDetail(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WindowPrefetchDetail_WindowDetail(const WindowPrefetchDetail_WindowDetail& from);
  WindowPrefetchDetail_WindowDetail(WindowPrefetchDetail_WindowDetail&& from) noexcept
    : WindowPrefetchDetail_WindowDetail() {
    *this = ::std::move(from);
  }

  inline WindowPrefetchDetail_WindowDetail& operator=(const WindowPrefetchDetail_WindowDetail& from) {
    CopyFrom(from);
    return *this;
  }
  inline WindowPrefetchDetail_WindowDetail& operator=(WindowPrefetchDetail_WindowDetail&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WindowPrefetchDetail_WindowDetail& default_instance() {
    return *internal_default_instance();
  }
  static inline const WindowPrefetchDetail_WindowDetail* internal_default_instance() {
    return reinterpret_cast<const WindowPrefetchDetail_WindowDetail*>(
               &_WindowPrefetchDetail_WindowDetail_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(WindowPrefetchDetail_WindowDetail& a, WindowPrefetchDetail_WindowDetail& b) {
    a.Swap(&b);
  }
  inline void Swap(WindowPrefetchDetail_WindowDetail* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WindowPrefetchDetail_WindowDetail* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WindowPrefetchDetail_WindowDetail* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WindowPrefetchDetail_WindowDetail>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WindowPrefetchDetail_WindowDetail& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WindowPrefetchDetail_WindowDetail& from) {
    WindowPrefetchDetail_WindowDetail::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WindowPrefetchDetail_WindowDetail* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.WindowPrefetchDetail.WindowDetail";
  }
  protected:
  explicit WindowPrefetchDetail_WindowDetail(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperandFieldNumber = 1,
    kSizeFieldNumber = 2,
  };
  // int64 operand = 1;
  void clear_operand();
  int64_t operand() const;
  void set_operand(int64_t value);
  private:
  int64_t _internal_operand() const;
  void _internal_set_operand(int64_t value);
  public:

  // int64 size = 2;
  void clear_size();
  int64_t size() const;
  void set_size(int64_t value);
  private:
  int64_t _internal_size() const;
  void _internal_set_size(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.WindowPrefetchDetail.WindowDetail)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t operand_;
    int64_t size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class WindowPrefetchDetail final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.WindowPrefetchDetail) */ {
 public:
  inline WindowPrefetchDetail() : WindowPrefetchDetail(nullptr) {}
  ~WindowPrefetchDetail() override;
  explicit PROTOBUF_CONSTEXPR WindowPrefetchDetail(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WindowPrefetchDetail(const WindowPrefetchDetail& from);
  WindowPrefetchDetail(WindowPrefetchDetail&& from) noexcept
    : WindowPrefetchDetail() {
    *this = ::std::move(from);
  }

  inline WindowPrefetchDetail& operator=(const WindowPrefetchDetail& from) {
    CopyFrom(from);
    return *this;
  }
  inline WindowPrefetchDetail& operator=(WindowPrefetchDetail&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WindowPrefetchDetail& default_instance() {
    return *internal_default_instance();
  }
  static inline const WindowPrefetchDetail* internal_default_instance() {
    return reinterpret_cast<const WindowPrefetchDetail*>(
               &_WindowPrefetchDetail_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(WindowPrefetchDetail& a, WindowPrefetchDetail& b) {
    a.Swap(&b);
  }
  inline void Swap(WindowPrefetchDetail* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WindowPrefetchDetail* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WindowPrefetchDetail* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WindowPrefetchDetail>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WindowPrefetchDetail& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WindowPrefetchDetail& from) {
    WindowPrefetchDetail::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WindowPrefetchDetail* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.WindowPrefetchDetail";
  }
  protected:
  explicit WindowPrefetchDetail(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef WindowPrefetchDetail_WindowDetail WindowDetail;

  // accessors -------------------------------------------------------

  enum : int {
    kWindowsFieldNumber = 1,
  };
  // repeated .xla.memory_space_assignment.WindowPrefetchDetail.WindowDetail windows = 1;
  int windows_size() const;
  private:
  int _internal_windows_size() const;
  public:
  void clear_windows();
  ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail* mutable_windows(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail >*
      mutable_windows();
  private:
  const ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail& _internal_windows(int index) const;
  ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail* _internal_add_windows();
  public:
  const ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail& windows(int index) const;
  ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail* add_windows();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail >&
      windows() const;

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.WindowPrefetchDetail)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail > windows_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class MemoryBoundLoopOptimizerOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions) */ {
 public:
  inline MemoryBoundLoopOptimizerOptions() : MemoryBoundLoopOptimizerOptions(nullptr) {}
  ~MemoryBoundLoopOptimizerOptions() override;
  explicit PROTOBUF_CONSTEXPR MemoryBoundLoopOptimizerOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryBoundLoopOptimizerOptions(const MemoryBoundLoopOptimizerOptions& from);
  MemoryBoundLoopOptimizerOptions(MemoryBoundLoopOptimizerOptions&& from) noexcept
    : MemoryBoundLoopOptimizerOptions() {
    *this = ::std::move(from);
  }

  inline MemoryBoundLoopOptimizerOptions& operator=(const MemoryBoundLoopOptimizerOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryBoundLoopOptimizerOptions& operator=(MemoryBoundLoopOptimizerOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryBoundLoopOptimizerOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryBoundLoopOptimizerOptions* internal_default_instance() {
    return reinterpret_cast<const MemoryBoundLoopOptimizerOptions*>(
               &_MemoryBoundLoopOptimizerOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MemoryBoundLoopOptimizerOptions& a, MemoryBoundLoopOptimizerOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryBoundLoopOptimizerOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryBoundLoopOptimizerOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryBoundLoopOptimizerOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryBoundLoopOptimizerOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryBoundLoopOptimizerOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryBoundLoopOptimizerOptions& from) {
    MemoryBoundLoopOptimizerOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryBoundLoopOptimizerOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions";
  }
  protected:
  explicit MemoryBoundLoopOptimizerOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDesiredCopyRatioFieldNumber = 2,
    kEnabledFieldNumber = 1,
    kAllowUnsatisfiedFullyPipelinedPrefetchFieldNumber = 3,
    kMinNumIterationsFieldNumber = 4,
  };
  // optional float desired_copy_ratio = 2;
  bool has_desired_copy_ratio() const;
  private:
  bool _internal_has_desired_copy_ratio() const;
  public:
  void clear_desired_copy_ratio();
  float desired_copy_ratio() const;
  void set_desired_copy_ratio(float value);
  private:
  float _internal_desired_copy_ratio() const;
  void _internal_set_desired_copy_ratio(float value);
  public:

  // optional bool enabled = 1;
  bool has_enabled() const;
  private:
  bool _internal_has_enabled() const;
  public:
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // optional bool allow_unsatisfied_fully_pipelined_prefetch = 3;
  bool has_allow_unsatisfied_fully_pipelined_prefetch() const;
  private:
  bool _internal_has_allow_unsatisfied_fully_pipelined_prefetch() const;
  public:
  void clear_allow_unsatisfied_fully_pipelined_prefetch();
  bool allow_unsatisfied_fully_pipelined_prefetch() const;
  void set_allow_unsatisfied_fully_pipelined_prefetch(bool value);
  private:
  bool _internal_allow_unsatisfied_fully_pipelined_prefetch() const;
  void _internal_set_allow_unsatisfied_fully_pipelined_prefetch(bool value);
  public:

  // optional float min_num_iterations = 4;
  bool has_min_num_iterations() const;
  private:
  bool _internal_has_min_num_iterations() const;
  public:
  void clear_min_num_iterations();
  float min_num_iterations() const;
  void set_min_num_iterations(float value);
  private:
  float _internal_min_num_iterations() const;
  void _internal_set_min_num_iterations(float value);
  public:

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    float desired_copy_ratio_;
    bool enabled_;
    bool allow_unsatisfied_fully_pipelined_prefetch_;
    float min_num_iterations_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class TupleShapeIndex final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.TupleShapeIndex) */ {
 public:
  inline TupleShapeIndex() : TupleShapeIndex(nullptr) {}
  ~TupleShapeIndex() override;
  explicit PROTOBUF_CONSTEXPR TupleShapeIndex(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TupleShapeIndex(const TupleShapeIndex& from);
  TupleShapeIndex(TupleShapeIndex&& from) noexcept
    : TupleShapeIndex() {
    *this = ::std::move(from);
  }

  inline TupleShapeIndex& operator=(const TupleShapeIndex& from) {
    CopyFrom(from);
    return *this;
  }
  inline TupleShapeIndex& operator=(TupleShapeIndex&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TupleShapeIndex& default_instance() {
    return *internal_default_instance();
  }
  static inline const TupleShapeIndex* internal_default_instance() {
    return reinterpret_cast<const TupleShapeIndex*>(
               &_TupleShapeIndex_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TupleShapeIndex& a, TupleShapeIndex& b) {
    a.Swap(&b);
  }
  inline void Swap(TupleShapeIndex* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TupleShapeIndex* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TupleShapeIndex* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TupleShapeIndex>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TupleShapeIndex& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TupleShapeIndex& from) {
    TupleShapeIndex::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TupleShapeIndex* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.TupleShapeIndex";
  }
  protected:
  explicit TupleShapeIndex(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIndexFieldNumber = 1,
  };
  // repeated int64 index = 1;
  int index_size() const;
  private:
  int _internal_index_size() const;
  public:
  void clear_index();
  private:
  int64_t _internal_index(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_index() const;
  void _internal_add_index(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_index();
  public:
  int64_t index(int index) const;
  void set_index(int index, int64_t value);
  void add_index(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      index() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_index();

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.TupleShapeIndex)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > index_;
    mutable std::atomic<int> _index_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class HloOperandFilter final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.HloOperandFilter) */ {
 public:
  inline HloOperandFilter() : HloOperandFilter(nullptr) {}
  ~HloOperandFilter() override;
  explicit PROTOBUF_CONSTEXPR HloOperandFilter(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HloOperandFilter(const HloOperandFilter& from);
  HloOperandFilter(HloOperandFilter&& from) noexcept
    : HloOperandFilter() {
    *this = ::std::move(from);
  }

  inline HloOperandFilter& operator=(const HloOperandFilter& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloOperandFilter& operator=(HloOperandFilter&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HloOperandFilter& default_instance() {
    return *internal_default_instance();
  }
  static inline const HloOperandFilter* internal_default_instance() {
    return reinterpret_cast<const HloOperandFilter*>(
               &_HloOperandFilter_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(HloOperandFilter& a, HloOperandFilter& b) {
    a.Swap(&b);
  }
  inline void Swap(HloOperandFilter* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloOperandFilter* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HloOperandFilter* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HloOperandFilter>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HloOperandFilter& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HloOperandFilter& from) {
    HloOperandFilter::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloOperandFilter* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.HloOperandFilter";
  }
  protected:
  explicit HloOperandFilter(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstructionNameRegexFieldNumber = 1,
    kInstructionRegexFieldNumber = 6,
    kTupleIndexFieldNumber = 5,
    kOperandNumberFieldNumber = 2,
    kSizeGteFieldNumber = 3,
    kSizeLteFieldNumber = 4,
  };
  // optional string instruction_name_regex = 1;
  bool has_instruction_name_regex() const;
  private:
  bool _internal_has_instruction_name_regex() const;
  public:
  void clear_instruction_name_regex();
  const std::string& instruction_name_regex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_instruction_name_regex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_instruction_name_regex();
  PROTOBUF_NODISCARD std::string* release_instruction_name_regex();
  void set_allocated_instruction_name_regex(std::string* instruction_name_regex);
  private:
  const std::string& _internal_instruction_name_regex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_instruction_name_regex(const std::string& value);
  std::string* _internal_mutable_instruction_name_regex();
  public:

  // optional string instruction_regex = 6;
  bool has_instruction_regex() const;
  private:
  bool _internal_has_instruction_regex() const;
  public:
  void clear_instruction_regex();
  const std::string& instruction_regex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_instruction_regex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_instruction_regex();
  PROTOBUF_NODISCARD std::string* release_instruction_regex();
  void set_allocated_instruction_regex(std::string* instruction_regex);
  private:
  const std::string& _internal_instruction_regex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_instruction_regex(const std::string& value);
  std::string* _internal_mutable_instruction_regex();
  public:

  // optional .xla.memory_space_assignment.TupleShapeIndex tuple_index = 5;
  bool has_tuple_index() const;
  private:
  bool _internal_has_tuple_index() const;
  public:
  void clear_tuple_index();
  const ::xla::memory_space_assignment::TupleShapeIndex& tuple_index() const;
  PROTOBUF_NODISCARD ::xla::memory_space_assignment::TupleShapeIndex* release_tuple_index();
  ::xla::memory_space_assignment::TupleShapeIndex* mutable_tuple_index();
  void set_allocated_tuple_index(::xla::memory_space_assignment::TupleShapeIndex* tuple_index);
  private:
  const ::xla::memory_space_assignment::TupleShapeIndex& _internal_tuple_index() const;
  ::xla::memory_space_assignment::TupleShapeIndex* _internal_mutable_tuple_index();
  public:
  void unsafe_arena_set_allocated_tuple_index(
      ::xla::memory_space_assignment::TupleShapeIndex* tuple_index);
  ::xla::memory_space_assignment::TupleShapeIndex* unsafe_arena_release_tuple_index();

  // optional int64 operand_number = 2;
  bool has_operand_number() const;
  private:
  bool _internal_has_operand_number() const;
  public:
  void clear_operand_number();
  int64_t operand_number() const;
  void set_operand_number(int64_t value);
  private:
  int64_t _internal_operand_number() const;
  void _internal_set_operand_number(int64_t value);
  public:

  // optional int64 size_gte = 3;
  bool has_size_gte() const;
  private:
  bool _internal_has_size_gte() const;
  public:
  void clear_size_gte();
  int64_t size_gte() const;
  void set_size_gte(int64_t value);
  private:
  int64_t _internal_size_gte() const;
  void _internal_set_size_gte(int64_t value);
  public:

  // optional int64 size_lte = 4;
  bool has_size_lte() const;
  private:
  bool _internal_has_size_lte() const;
  public:
  void clear_size_lte();
  int64_t size_lte() const;
  void set_size_lte(int64_t value);
  private:
  int64_t _internal_size_lte() const;
  void _internal_set_size_lte(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.HloOperandFilter)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr instruction_name_regex_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr instruction_regex_;
    ::xla::memory_space_assignment::TupleShapeIndex* tuple_index_;
    int64_t operand_number_;
    int64_t size_gte_;
    int64_t size_lte_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class PreferredPrefetchOverrideOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.PreferredPrefetchOverrideOptions) */ {
 public:
  inline PreferredPrefetchOverrideOptions() : PreferredPrefetchOverrideOptions(nullptr) {}
  ~PreferredPrefetchOverrideOptions() override;
  explicit PROTOBUF_CONSTEXPR PreferredPrefetchOverrideOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PreferredPrefetchOverrideOptions(const PreferredPrefetchOverrideOptions& from);
  PreferredPrefetchOverrideOptions(PreferredPrefetchOverrideOptions&& from) noexcept
    : PreferredPrefetchOverrideOptions() {
    *this = ::std::move(from);
  }

  inline PreferredPrefetchOverrideOptions& operator=(const PreferredPrefetchOverrideOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline PreferredPrefetchOverrideOptions& operator=(PreferredPrefetchOverrideOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PreferredPrefetchOverrideOptions& default_instance() {
    return *internal_default_instance();
  }
  enum OptionsCase {
    kPrefetchEagerness = 1,
    kAfterInstruction = 4,
    kBeforeInstruction = 5,
    OPTIONS_NOT_SET = 0,
  };

  static inline const PreferredPrefetchOverrideOptions* internal_default_instance() {
    return reinterpret_cast<const PreferredPrefetchOverrideOptions*>(
               &_PreferredPrefetchOverrideOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(PreferredPrefetchOverrideOptions& a, PreferredPrefetchOverrideOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(PreferredPrefetchOverrideOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PreferredPrefetchOverrideOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PreferredPrefetchOverrideOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PreferredPrefetchOverrideOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PreferredPrefetchOverrideOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PreferredPrefetchOverrideOptions& from) {
    PreferredPrefetchOverrideOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PreferredPrefetchOverrideOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.PreferredPrefetchOverrideOptions";
  }
  protected:
  explicit PreferredPrefetchOverrideOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPrefetchEagernessFieldNumber = 1,
    kAfterInstructionFieldNumber = 4,
    kBeforeInstructionFieldNumber = 5,
  };
  // float prefetch_eagerness = 1;
  bool has_prefetch_eagerness() const;
  private:
  bool _internal_has_prefetch_eagerness() const;
  public:
  void clear_prefetch_eagerness();
  float prefetch_eagerness() const;
  void set_prefetch_eagerness(float value);
  private:
  float _internal_prefetch_eagerness() const;
  void _internal_set_prefetch_eagerness(float value);
  public:

  // .xla.memory_space_assignment.HloPositionMatcher after_instruction = 4;
  bool has_after_instruction() const;
  private:
  bool _internal_has_after_instruction() const;
  public:
  void clear_after_instruction();
  const ::xla::memory_space_assignment::HloPositionMatcher& after_instruction() const;
  PROTOBUF_NODISCARD ::xla::memory_space_assignment::HloPositionMatcher* release_after_instruction();
  ::xla::memory_space_assignment::HloPositionMatcher* mutable_after_instruction();
  void set_allocated_after_instruction(::xla::memory_space_assignment::HloPositionMatcher* after_instruction);
  private:
  const ::xla::memory_space_assignment::HloPositionMatcher& _internal_after_instruction() const;
  ::xla::memory_space_assignment::HloPositionMatcher* _internal_mutable_after_instruction();
  public:
  void unsafe_arena_set_allocated_after_instruction(
      ::xla::memory_space_assignment::HloPositionMatcher* after_instruction);
  ::xla::memory_space_assignment::HloPositionMatcher* unsafe_arena_release_after_instruction();

  // .xla.memory_space_assignment.HloPositionMatcher before_instruction = 5;
  bool has_before_instruction() const;
  private:
  bool _internal_has_before_instruction() const;
  public:
  void clear_before_instruction();
  const ::xla::memory_space_assignment::HloPositionMatcher& before_instruction() const;
  PROTOBUF_NODISCARD ::xla::memory_space_assignment::HloPositionMatcher* release_before_instruction();
  ::xla::memory_space_assignment::HloPositionMatcher* mutable_before_instruction();
  void set_allocated_before_instruction(::xla::memory_space_assignment::HloPositionMatcher* before_instruction);
  private:
  const ::xla::memory_space_assignment::HloPositionMatcher& _internal_before_instruction() const;
  ::xla::memory_space_assignment::HloPositionMatcher* _internal_mutable_before_instruction();
  public:
  void unsafe_arena_set_allocated_before_instruction(
      ::xla::memory_space_assignment::HloPositionMatcher* before_instruction);
  ::xla::memory_space_assignment::HloPositionMatcher* unsafe_arena_release_before_instruction();

  void clear_options();
  OptionsCase options_case() const;
  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.PreferredPrefetchOverrideOptions)
 private:
  class _Internal;
  void set_has_prefetch_eagerness();
  void set_has_after_instruction();
  void set_has_before_instruction();

  inline bool has_options() const;
  inline void clear_has_options();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union OptionsUnion {
      constexpr OptionsUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      float prefetch_eagerness_;
      ::xla::memory_space_assignment::HloPositionMatcher* after_instruction_;
      ::xla::memory_space_assignment::HloPositionMatcher* before_instruction_;
    } options_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class PreferredPrefetchOverride final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.PreferredPrefetchOverride) */ {
 public:
  inline PreferredPrefetchOverride() : PreferredPrefetchOverride(nullptr) {}
  ~PreferredPrefetchOverride() override;
  explicit PROTOBUF_CONSTEXPR PreferredPrefetchOverride(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PreferredPrefetchOverride(const PreferredPrefetchOverride& from);
  PreferredPrefetchOverride(PreferredPrefetchOverride&& from) noexcept
    : PreferredPrefetchOverride() {
    *this = ::std::move(from);
  }

  inline PreferredPrefetchOverride& operator=(const PreferredPrefetchOverride& from) {
    CopyFrom(from);
    return *this;
  }
  inline PreferredPrefetchOverride& operator=(PreferredPrefetchOverride&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PreferredPrefetchOverride& default_instance() {
    return *internal_default_instance();
  }
  static inline const PreferredPrefetchOverride* internal_default_instance() {
    return reinterpret_cast<const PreferredPrefetchOverride*>(
               &_PreferredPrefetchOverride_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(PreferredPrefetchOverride& a, PreferredPrefetchOverride& b) {
    a.Swap(&b);
  }
  inline void Swap(PreferredPrefetchOverride* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PreferredPrefetchOverride* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PreferredPrefetchOverride* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PreferredPrefetchOverride>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PreferredPrefetchOverride& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PreferredPrefetchOverride& from) {
    PreferredPrefetchOverride::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PreferredPrefetchOverride* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.PreferredPrefetchOverride";
  }
  protected:
  explicit PreferredPrefetchOverride(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHloOperandFilterFieldNumber = 1,
    kOverrideOptionsFieldNumber = 2,
  };
  // optional .xla.memory_space_assignment.HloOperandFilter hlo_operand_filter = 1;
  bool has_hlo_operand_filter() const;
  private:
  bool _internal_has_hlo_operand_filter() const;
  public:
  void clear_hlo_operand_filter();
  const ::xla::memory_space_assignment::HloOperandFilter& hlo_operand_filter() const;
  PROTOBUF_NODISCARD ::xla::memory_space_assignment::HloOperandFilter* release_hlo_operand_filter();
  ::xla::memory_space_assignment::HloOperandFilter* mutable_hlo_operand_filter();
  void set_allocated_hlo_operand_filter(::xla::memory_space_assignment::HloOperandFilter* hlo_operand_filter);
  private:
  const ::xla::memory_space_assignment::HloOperandFilter& _internal_hlo_operand_filter() const;
  ::xla::memory_space_assignment::HloOperandFilter* _internal_mutable_hlo_operand_filter();
  public:
  void unsafe_arena_set_allocated_hlo_operand_filter(
      ::xla::memory_space_assignment::HloOperandFilter* hlo_operand_filter);
  ::xla::memory_space_assignment::HloOperandFilter* unsafe_arena_release_hlo_operand_filter();

  // optional .xla.memory_space_assignment.PreferredPrefetchOverrideOptions override_options = 2;
  bool has_override_options() const;
  private:
  bool _internal_has_override_options() const;
  public:
  void clear_override_options();
  const ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions& override_options() const;
  PROTOBUF_NODISCARD ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* release_override_options();
  ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* mutable_override_options();
  void set_allocated_override_options(::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* override_options);
  private:
  const ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions& _internal_override_options() const;
  ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* _internal_mutable_override_options();
  public:
  void unsafe_arena_set_allocated_override_options(
      ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* override_options);
  ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* unsafe_arena_release_override_options();

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.PreferredPrefetchOverride)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::xla::memory_space_assignment::HloOperandFilter* hlo_operand_filter_;
    ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* override_options_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class PreferredPrefetchOverrides final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.PreferredPrefetchOverrides) */ {
 public:
  inline PreferredPrefetchOverrides() : PreferredPrefetchOverrides(nullptr) {}
  ~PreferredPrefetchOverrides() override;
  explicit PROTOBUF_CONSTEXPR PreferredPrefetchOverrides(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PreferredPrefetchOverrides(const PreferredPrefetchOverrides& from);
  PreferredPrefetchOverrides(PreferredPrefetchOverrides&& from) noexcept
    : PreferredPrefetchOverrides() {
    *this = ::std::move(from);
  }

  inline PreferredPrefetchOverrides& operator=(const PreferredPrefetchOverrides& from) {
    CopyFrom(from);
    return *this;
  }
  inline PreferredPrefetchOverrides& operator=(PreferredPrefetchOverrides&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PreferredPrefetchOverrides& default_instance() {
    return *internal_default_instance();
  }
  static inline const PreferredPrefetchOverrides* internal_default_instance() {
    return reinterpret_cast<const PreferredPrefetchOverrides*>(
               &_PreferredPrefetchOverrides_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(PreferredPrefetchOverrides& a, PreferredPrefetchOverrides& b) {
    a.Swap(&b);
  }
  inline void Swap(PreferredPrefetchOverrides* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PreferredPrefetchOverrides* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PreferredPrefetchOverrides* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PreferredPrefetchOverrides>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PreferredPrefetchOverrides& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PreferredPrefetchOverrides& from) {
    PreferredPrefetchOverrides::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PreferredPrefetchOverrides* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.PreferredPrefetchOverrides";
  }
  protected:
  explicit PreferredPrefetchOverrides(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOverridesFieldNumber = 1,
  };
  // repeated .xla.memory_space_assignment.PreferredPrefetchOverride overrides = 1;
  int overrides_size() const;
  private:
  int _internal_overrides_size() const;
  public:
  void clear_overrides();
  ::xla::memory_space_assignment::PreferredPrefetchOverride* mutable_overrides(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::PreferredPrefetchOverride >*
      mutable_overrides();
  private:
  const ::xla::memory_space_assignment::PreferredPrefetchOverride& _internal_overrides(int index) const;
  ::xla::memory_space_assignment::PreferredPrefetchOverride* _internal_add_overrides();
  public:
  const ::xla::memory_space_assignment::PreferredPrefetchOverride& overrides(int index) const;
  ::xla::memory_space_assignment::PreferredPrefetchOverride* add_overrides();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::PreferredPrefetchOverride >&
      overrides() const;

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.PreferredPrefetchOverrides)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::PreferredPrefetchOverride > overrides_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class HloPositionMatcher final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.HloPositionMatcher) */ {
 public:
  inline HloPositionMatcher() : HloPositionMatcher(nullptr) {}
  ~HloPositionMatcher() override;
  explicit PROTOBUF_CONSTEXPR HloPositionMatcher(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HloPositionMatcher(const HloPositionMatcher& from);
  HloPositionMatcher(HloPositionMatcher&& from) noexcept
    : HloPositionMatcher() {
    *this = ::std::move(from);
  }

  inline HloPositionMatcher& operator=(const HloPositionMatcher& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloPositionMatcher& operator=(HloPositionMatcher&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HloPositionMatcher& default_instance() {
    return *internal_default_instance();
  }
  static inline const HloPositionMatcher* internal_default_instance() {
    return reinterpret_cast<const HloPositionMatcher*>(
               &_HloPositionMatcher_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(HloPositionMatcher& a, HloPositionMatcher& b) {
    a.Swap(&b);
  }
  inline void Swap(HloPositionMatcher* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloPositionMatcher* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HloPositionMatcher* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HloPositionMatcher>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HloPositionMatcher& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HloPositionMatcher& from) {
    HloPositionMatcher::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloPositionMatcher* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.HloPositionMatcher";
  }
  protected:
  explicit HloPositionMatcher(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstructionRegexFieldNumber = 1,
    kInstructionNameRegexFieldNumber = 2,
    kTupleIndexFieldNumber = 3,
    kHloUseFilterFieldNumber = 6,
    kSizeGteFieldNumber = 4,
    kSizeLteFieldNumber = 5,
  };
  // optional string instruction_regex = 1;
  bool has_instruction_regex() const;
  private:
  bool _internal_has_instruction_regex() const;
  public:
  void clear_instruction_regex();
  const std::string& instruction_regex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_instruction_regex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_instruction_regex();
  PROTOBUF_NODISCARD std::string* release_instruction_regex();
  void set_allocated_instruction_regex(std::string* instruction_regex);
  private:
  const std::string& _internal_instruction_regex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_instruction_regex(const std::string& value);
  std::string* _internal_mutable_instruction_regex();
  public:

  // optional string instruction_name_regex = 2;
  bool has_instruction_name_regex() const;
  private:
  bool _internal_has_instruction_name_regex() const;
  public:
  void clear_instruction_name_regex();
  const std::string& instruction_name_regex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_instruction_name_regex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_instruction_name_regex();
  PROTOBUF_NODISCARD std::string* release_instruction_name_regex();
  void set_allocated_instruction_name_regex(std::string* instruction_name_regex);
  private:
  const std::string& _internal_instruction_name_regex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_instruction_name_regex(const std::string& value);
  std::string* _internal_mutable_instruction_name_regex();
  public:

  // optional .xla.memory_space_assignment.TupleShapeIndex tuple_index = 3;
  bool has_tuple_index() const;
  private:
  bool _internal_has_tuple_index() const;
  public:
  void clear_tuple_index();
  const ::xla::memory_space_assignment::TupleShapeIndex& tuple_index() const;
  PROTOBUF_NODISCARD ::xla::memory_space_assignment::TupleShapeIndex* release_tuple_index();
  ::xla::memory_space_assignment::TupleShapeIndex* mutable_tuple_index();
  void set_allocated_tuple_index(::xla::memory_space_assignment::TupleShapeIndex* tuple_index);
  private:
  const ::xla::memory_space_assignment::TupleShapeIndex& _internal_tuple_index() const;
  ::xla::memory_space_assignment::TupleShapeIndex* _internal_mutable_tuple_index();
  public:
  void unsafe_arena_set_allocated_tuple_index(
      ::xla::memory_space_assignment::TupleShapeIndex* tuple_index);
  ::xla::memory_space_assignment::TupleShapeIndex* unsafe_arena_release_tuple_index();

  // optional .xla.memory_space_assignment.HloOperandFilter hlo_use_filter = 6;
  bool has_hlo_use_filter() const;
  private:
  bool _internal_has_hlo_use_filter() const;
  public:
  void clear_hlo_use_filter();
  const ::xla::memory_space_assignment::HloOperandFilter& hlo_use_filter() const;
  PROTOBUF_NODISCARD ::xla::memory_space_assignment::HloOperandFilter* release_hlo_use_filter();
  ::xla::memory_space_assignment::HloOperandFilter* mutable_hlo_use_filter();
  void set_allocated_hlo_use_filter(::xla::memory_space_assignment::HloOperandFilter* hlo_use_filter);
  private:
  const ::xla::memory_space_assignment::HloOperandFilter& _internal_hlo_use_filter() const;
  ::xla::memory_space_assignment::HloOperandFilter* _internal_mutable_hlo_use_filter();
  public:
  void unsafe_arena_set_allocated_hlo_use_filter(
      ::xla::memory_space_assignment::HloOperandFilter* hlo_use_filter);
  ::xla::memory_space_assignment::HloOperandFilter* unsafe_arena_release_hlo_use_filter();

  // optional int64 size_gte = 4;
  bool has_size_gte() const;
  private:
  bool _internal_has_size_gte() const;
  public:
  void clear_size_gte();
  int64_t size_gte() const;
  void set_size_gte(int64_t value);
  private:
  int64_t _internal_size_gte() const;
  void _internal_set_size_gte(int64_t value);
  public:

  // optional int64 size_lte = 5;
  bool has_size_lte() const;
  private:
  bool _internal_has_size_lte() const;
  public:
  void clear_size_lte();
  int64_t size_lte() const;
  void set_size_lte(int64_t value);
  private:
  int64_t _internal_size_lte() const;
  void _internal_set_size_lte(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.HloPositionMatcher)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr instruction_regex_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr instruction_name_regex_;
    ::xla::memory_space_assignment::TupleShapeIndex* tuple_index_;
    ::xla::memory_space_assignment::HloOperandFilter* hlo_use_filter_;
    int64_t size_gte_;
    int64_t size_lte_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class MsaSortOrderOverrideOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.MsaSortOrderOverrideOptions) */ {
 public:
  inline MsaSortOrderOverrideOptions() : MsaSortOrderOverrideOptions(nullptr) {}
  ~MsaSortOrderOverrideOptions() override;
  explicit PROTOBUF_CONSTEXPR MsaSortOrderOverrideOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsaSortOrderOverrideOptions(const MsaSortOrderOverrideOptions& from);
  MsaSortOrderOverrideOptions(MsaSortOrderOverrideOptions&& from) noexcept
    : MsaSortOrderOverrideOptions() {
    *this = ::std::move(from);
  }

  inline MsaSortOrderOverrideOptions& operator=(const MsaSortOrderOverrideOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsaSortOrderOverrideOptions& operator=(MsaSortOrderOverrideOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsaSortOrderOverrideOptions& default_instance() {
    return *internal_default_instance();
  }
  enum OptionsCase {
    kAssignFirst = 1,
    kAssignLast = 2,
    OPTIONS_NOT_SET = 0,
  };

  static inline const MsaSortOrderOverrideOptions* internal_default_instance() {
    return reinterpret_cast<const MsaSortOrderOverrideOptions*>(
               &_MsaSortOrderOverrideOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(MsaSortOrderOverrideOptions& a, MsaSortOrderOverrideOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(MsaSortOrderOverrideOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsaSortOrderOverrideOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsaSortOrderOverrideOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsaSortOrderOverrideOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsaSortOrderOverrideOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MsaSortOrderOverrideOptions& from) {
    MsaSortOrderOverrideOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsaSortOrderOverrideOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.MsaSortOrderOverrideOptions";
  }
  protected:
  explicit MsaSortOrderOverrideOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAssignFirstFieldNumber = 1,
    kAssignLastFieldNumber = 2,
  };
  // bool assign_first = 1;
  bool has_assign_first() const;
  private:
  bool _internal_has_assign_first() const;
  public:
  void clear_assign_first();
  bool assign_first() const;
  void set_assign_first(bool value);
  private:
  bool _internal_assign_first() const;
  void _internal_set_assign_first(bool value);
  public:

  // bool assign_last = 2;
  bool has_assign_last() const;
  private:
  bool _internal_has_assign_last() const;
  public:
  void clear_assign_last();
  bool assign_last() const;
  void set_assign_last(bool value);
  private:
  bool _internal_assign_last() const;
  void _internal_set_assign_last(bool value);
  public:

  void clear_options();
  OptionsCase options_case() const;
  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.MsaSortOrderOverrideOptions)
 private:
  class _Internal;
  void set_has_assign_first();
  void set_has_assign_last();

  inline bool has_options() const;
  inline void clear_has_options();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union OptionsUnion {
      constexpr OptionsUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      bool assign_first_;
      bool assign_last_;
    } options_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class MsaSortOrderOverride final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.MsaSortOrderOverride) */ {
 public:
  inline MsaSortOrderOverride() : MsaSortOrderOverride(nullptr) {}
  ~MsaSortOrderOverride() override;
  explicit PROTOBUF_CONSTEXPR MsaSortOrderOverride(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsaSortOrderOverride(const MsaSortOrderOverride& from);
  MsaSortOrderOverride(MsaSortOrderOverride&& from) noexcept
    : MsaSortOrderOverride() {
    *this = ::std::move(from);
  }

  inline MsaSortOrderOverride& operator=(const MsaSortOrderOverride& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsaSortOrderOverride& operator=(MsaSortOrderOverride&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsaSortOrderOverride& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsaSortOrderOverride* internal_default_instance() {
    return reinterpret_cast<const MsaSortOrderOverride*>(
               &_MsaSortOrderOverride_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(MsaSortOrderOverride& a, MsaSortOrderOverride& b) {
    a.Swap(&b);
  }
  inline void Swap(MsaSortOrderOverride* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsaSortOrderOverride* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsaSortOrderOverride* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsaSortOrderOverride>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsaSortOrderOverride& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MsaSortOrderOverride& from) {
    MsaSortOrderOverride::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsaSortOrderOverride* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.MsaSortOrderOverride";
  }
  protected:
  explicit MsaSortOrderOverride(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHloPositionMatcherFieldNumber = 1,
    kOverrideOptionsFieldNumber = 2,
    kApplyToCrossProgramPrefetchesFieldNumber = 3,
  };
  // optional .xla.memory_space_assignment.HloPositionMatcher hlo_position_matcher = 1;
  bool has_hlo_position_matcher() const;
  private:
  bool _internal_has_hlo_position_matcher() const;
  public:
  void clear_hlo_position_matcher();
  const ::xla::memory_space_assignment::HloPositionMatcher& hlo_position_matcher() const;
  PROTOBUF_NODISCARD ::xla::memory_space_assignment::HloPositionMatcher* release_hlo_position_matcher();
  ::xla::memory_space_assignment::HloPositionMatcher* mutable_hlo_position_matcher();
  void set_allocated_hlo_position_matcher(::xla::memory_space_assignment::HloPositionMatcher* hlo_position_matcher);
  private:
  const ::xla::memory_space_assignment::HloPositionMatcher& _internal_hlo_position_matcher() const;
  ::xla::memory_space_assignment::HloPositionMatcher* _internal_mutable_hlo_position_matcher();
  public:
  void unsafe_arena_set_allocated_hlo_position_matcher(
      ::xla::memory_space_assignment::HloPositionMatcher* hlo_position_matcher);
  ::xla::memory_space_assignment::HloPositionMatcher* unsafe_arena_release_hlo_position_matcher();

  // optional .xla.memory_space_assignment.MsaSortOrderOverrideOptions override_options = 2;
  bool has_override_options() const;
  private:
  bool _internal_has_override_options() const;
  public:
  void clear_override_options();
  const ::xla::memory_space_assignment::MsaSortOrderOverrideOptions& override_options() const;
  PROTOBUF_NODISCARD ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* release_override_options();
  ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* mutable_override_options();
  void set_allocated_override_options(::xla::memory_space_assignment::MsaSortOrderOverrideOptions* override_options);
  private:
  const ::xla::memory_space_assignment::MsaSortOrderOverrideOptions& _internal_override_options() const;
  ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* _internal_mutable_override_options();
  public:
  void unsafe_arena_set_allocated_override_options(
      ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* override_options);
  ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* unsafe_arena_release_override_options();

  // optional bool apply_to_cross_program_prefetches = 3;
  bool has_apply_to_cross_program_prefetches() const;
  private:
  bool _internal_has_apply_to_cross_program_prefetches() const;
  public:
  void clear_apply_to_cross_program_prefetches();
  bool apply_to_cross_program_prefetches() const;
  void set_apply_to_cross_program_prefetches(bool value);
  private:
  bool _internal_apply_to_cross_program_prefetches() const;
  void _internal_set_apply_to_cross_program_prefetches(bool value);
  public:

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.MsaSortOrderOverride)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::xla::memory_space_assignment::HloPositionMatcher* hlo_position_matcher_;
    ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* override_options_;
    bool apply_to_cross_program_prefetches_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// -------------------------------------------------------------------

class MsaSortOrderOverrides final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.memory_space_assignment.MsaSortOrderOverrides) */ {
 public:
  inline MsaSortOrderOverrides() : MsaSortOrderOverrides(nullptr) {}
  ~MsaSortOrderOverrides() override;
  explicit PROTOBUF_CONSTEXPR MsaSortOrderOverrides(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsaSortOrderOverrides(const MsaSortOrderOverrides& from);
  MsaSortOrderOverrides(MsaSortOrderOverrides&& from) noexcept
    : MsaSortOrderOverrides() {
    *this = ::std::move(from);
  }

  inline MsaSortOrderOverrides& operator=(const MsaSortOrderOverrides& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsaSortOrderOverrides& operator=(MsaSortOrderOverrides&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsaSortOrderOverrides& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsaSortOrderOverrides* internal_default_instance() {
    return reinterpret_cast<const MsaSortOrderOverrides*>(
               &_MsaSortOrderOverrides_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(MsaSortOrderOverrides& a, MsaSortOrderOverrides& b) {
    a.Swap(&b);
  }
  inline void Swap(MsaSortOrderOverrides* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsaSortOrderOverrides* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsaSortOrderOverrides* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsaSortOrderOverrides>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsaSortOrderOverrides& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MsaSortOrderOverrides& from) {
    MsaSortOrderOverrides::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsaSortOrderOverrides* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.memory_space_assignment.MsaSortOrderOverrides";
  }
  protected:
  explicit MsaSortOrderOverrides(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOverridesFieldNumber = 1,
  };
  // repeated .xla.memory_space_assignment.MsaSortOrderOverride overrides = 1;
  int overrides_size() const;
  private:
  int _internal_overrides_size() const;
  public:
  void clear_overrides();
  ::xla::memory_space_assignment::MsaSortOrderOverride* mutable_overrides(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::MsaSortOrderOverride >*
      mutable_overrides();
  private:
  const ::xla::memory_space_assignment::MsaSortOrderOverride& _internal_overrides(int index) const;
  ::xla::memory_space_assignment::MsaSortOrderOverride* _internal_add_overrides();
  public:
  const ::xla::memory_space_assignment::MsaSortOrderOverride& overrides(int index) const;
  ::xla::memory_space_assignment::MsaSortOrderOverride* add_overrides();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::MsaSortOrderOverride >&
      overrides() const;

  // @@protoc_insertion_point(class_scope:xla.memory_space_assignment.MsaSortOrderOverrides)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::MsaSortOrderOverride > overrides_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SlicedPrefetchOptions

// uint32 max_slices = 1;
inline void SlicedPrefetchOptions::clear_max_slices() {
  _impl_.max_slices_ = 0u;
}
inline uint32_t SlicedPrefetchOptions::_internal_max_slices() const {
  return _impl_.max_slices_;
}
inline uint32_t SlicedPrefetchOptions::max_slices() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.SlicedPrefetchOptions.max_slices)
  return _internal_max_slices();
}
inline void SlicedPrefetchOptions::_internal_set_max_slices(uint32_t value) {
  
  _impl_.max_slices_ = value;
}
inline void SlicedPrefetchOptions::set_max_slices(uint32_t value) {
  _internal_set_max_slices(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.SlicedPrefetchOptions.max_slices)
}

// uint64 min_bytes = 2;
inline void SlicedPrefetchOptions::clear_min_bytes() {
  _impl_.min_bytes_ = uint64_t{0u};
}
inline uint64_t SlicedPrefetchOptions::_internal_min_bytes() const {
  return _impl_.min_bytes_;
}
inline uint64_t SlicedPrefetchOptions::min_bytes() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.SlicedPrefetchOptions.min_bytes)
  return _internal_min_bytes();
}
inline void SlicedPrefetchOptions::_internal_set_min_bytes(uint64_t value) {
  
  _impl_.min_bytes_ = value;
}
inline void SlicedPrefetchOptions::set_min_bytes(uint64_t value) {
  _internal_set_min_bytes(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.SlicedPrefetchOptions.min_bytes)
}

// bool fail_on_non_alignment_boundary_slice_proposal = 3;
inline void SlicedPrefetchOptions::clear_fail_on_non_alignment_boundary_slice_proposal() {
  _impl_.fail_on_non_alignment_boundary_slice_proposal_ = false;
}
inline bool SlicedPrefetchOptions::_internal_fail_on_non_alignment_boundary_slice_proposal() const {
  return _impl_.fail_on_non_alignment_boundary_slice_proposal_;
}
inline bool SlicedPrefetchOptions::fail_on_non_alignment_boundary_slice_proposal() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.SlicedPrefetchOptions.fail_on_non_alignment_boundary_slice_proposal)
  return _internal_fail_on_non_alignment_boundary_slice_proposal();
}
inline void SlicedPrefetchOptions::_internal_set_fail_on_non_alignment_boundary_slice_proposal(bool value) {
  
  _impl_.fail_on_non_alignment_boundary_slice_proposal_ = value;
}
inline void SlicedPrefetchOptions::set_fail_on_non_alignment_boundary_slice_proposal(bool value) {
  _internal_set_fail_on_non_alignment_boundary_slice_proposal(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.SlicedPrefetchOptions.fail_on_non_alignment_boundary_slice_proposal)
}

// uint32 all_slice_time_permutations_threshold = 4;
inline void SlicedPrefetchOptions::clear_all_slice_time_permutations_threshold() {
  _impl_.all_slice_time_permutations_threshold_ = 0u;
}
inline uint32_t SlicedPrefetchOptions::_internal_all_slice_time_permutations_threshold() const {
  return _impl_.all_slice_time_permutations_threshold_;
}
inline uint32_t SlicedPrefetchOptions::all_slice_time_permutations_threshold() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.SlicedPrefetchOptions.all_slice_time_permutations_threshold)
  return _internal_all_slice_time_permutations_threshold();
}
inline void SlicedPrefetchOptions::_internal_set_all_slice_time_permutations_threshold(uint32_t value) {
  
  _impl_.all_slice_time_permutations_threshold_ = value;
}
inline void SlicedPrefetchOptions::set_all_slice_time_permutations_threshold(uint32_t value) {
  _internal_set_all_slice_time_permutations_threshold(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.SlicedPrefetchOptions.all_slice_time_permutations_threshold)
}

// uint64 preferred_slice_size = 5;
inline void SlicedPrefetchOptions::clear_preferred_slice_size() {
  _impl_.preferred_slice_size_ = uint64_t{0u};
}
inline uint64_t SlicedPrefetchOptions::_internal_preferred_slice_size() const {
  return _impl_.preferred_slice_size_;
}
inline uint64_t SlicedPrefetchOptions::preferred_slice_size() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.SlicedPrefetchOptions.preferred_slice_size)
  return _internal_preferred_slice_size();
}
inline void SlicedPrefetchOptions::_internal_set_preferred_slice_size(uint64_t value) {
  
  _impl_.preferred_slice_size_ = value;
}
inline void SlicedPrefetchOptions::set_preferred_slice_size(uint64_t value) {
  _internal_set_preferred_slice_size(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.SlicedPrefetchOptions.preferred_slice_size)
}

// -------------------------------------------------------------------

// WindowPrefetchDetail_WindowDetail

// int64 operand = 1;
inline void WindowPrefetchDetail_WindowDetail::clear_operand() {
  _impl_.operand_ = int64_t{0};
}
inline int64_t WindowPrefetchDetail_WindowDetail::_internal_operand() const {
  return _impl_.operand_;
}
inline int64_t WindowPrefetchDetail_WindowDetail::operand() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.WindowPrefetchDetail.WindowDetail.operand)
  return _internal_operand();
}
inline void WindowPrefetchDetail_WindowDetail::_internal_set_operand(int64_t value) {
  
  _impl_.operand_ = value;
}
inline void WindowPrefetchDetail_WindowDetail::set_operand(int64_t value) {
  _internal_set_operand(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.WindowPrefetchDetail.WindowDetail.operand)
}

// int64 size = 2;
inline void WindowPrefetchDetail_WindowDetail::clear_size() {
  _impl_.size_ = int64_t{0};
}
inline int64_t WindowPrefetchDetail_WindowDetail::_internal_size() const {
  return _impl_.size_;
}
inline int64_t WindowPrefetchDetail_WindowDetail::size() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.WindowPrefetchDetail.WindowDetail.size)
  return _internal_size();
}
inline void WindowPrefetchDetail_WindowDetail::_internal_set_size(int64_t value) {
  
  _impl_.size_ = value;
}
inline void WindowPrefetchDetail_WindowDetail::set_size(int64_t value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.WindowPrefetchDetail.WindowDetail.size)
}

// -------------------------------------------------------------------

// WindowPrefetchDetail

// repeated .xla.memory_space_assignment.WindowPrefetchDetail.WindowDetail windows = 1;
inline int WindowPrefetchDetail::_internal_windows_size() const {
  return _impl_.windows_.size();
}
inline int WindowPrefetchDetail::windows_size() const {
  return _internal_windows_size();
}
inline void WindowPrefetchDetail::clear_windows() {
  _impl_.windows_.Clear();
}
inline ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail* WindowPrefetchDetail::mutable_windows(int index) {
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.WindowPrefetchDetail.windows)
  return _impl_.windows_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail >*
WindowPrefetchDetail::mutable_windows() {
  // @@protoc_insertion_point(field_mutable_list:xla.memory_space_assignment.WindowPrefetchDetail.windows)
  return &_impl_.windows_;
}
inline const ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail& WindowPrefetchDetail::_internal_windows(int index) const {
  return _impl_.windows_.Get(index);
}
inline const ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail& WindowPrefetchDetail::windows(int index) const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.WindowPrefetchDetail.windows)
  return _internal_windows(index);
}
inline ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail* WindowPrefetchDetail::_internal_add_windows() {
  return _impl_.windows_.Add();
}
inline ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail* WindowPrefetchDetail::add_windows() {
  ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail* _add = _internal_add_windows();
  // @@protoc_insertion_point(field_add:xla.memory_space_assignment.WindowPrefetchDetail.windows)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::WindowPrefetchDetail_WindowDetail >&
WindowPrefetchDetail::windows() const {
  // @@protoc_insertion_point(field_list:xla.memory_space_assignment.WindowPrefetchDetail.windows)
  return _impl_.windows_;
}

// -------------------------------------------------------------------

// MemoryBoundLoopOptimizerOptions

// optional bool enabled = 1;
inline bool MemoryBoundLoopOptimizerOptions::_internal_has_enabled() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MemoryBoundLoopOptimizerOptions::has_enabled() const {
  return _internal_has_enabled();
}
inline void MemoryBoundLoopOptimizerOptions::clear_enabled() {
  _impl_.enabled_ = false;
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline bool MemoryBoundLoopOptimizerOptions::_internal_enabled() const {
  return _impl_.enabled_;
}
inline bool MemoryBoundLoopOptimizerOptions::enabled() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions.enabled)
  return _internal_enabled();
}
inline void MemoryBoundLoopOptimizerOptions::_internal_set_enabled(bool value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.enabled_ = value;
}
inline void MemoryBoundLoopOptimizerOptions::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions.enabled)
}

// optional float desired_copy_ratio = 2;
inline bool MemoryBoundLoopOptimizerOptions::_internal_has_desired_copy_ratio() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MemoryBoundLoopOptimizerOptions::has_desired_copy_ratio() const {
  return _internal_has_desired_copy_ratio();
}
inline void MemoryBoundLoopOptimizerOptions::clear_desired_copy_ratio() {
  _impl_.desired_copy_ratio_ = 0;
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline float MemoryBoundLoopOptimizerOptions::_internal_desired_copy_ratio() const {
  return _impl_.desired_copy_ratio_;
}
inline float MemoryBoundLoopOptimizerOptions::desired_copy_ratio() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions.desired_copy_ratio)
  return _internal_desired_copy_ratio();
}
inline void MemoryBoundLoopOptimizerOptions::_internal_set_desired_copy_ratio(float value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.desired_copy_ratio_ = value;
}
inline void MemoryBoundLoopOptimizerOptions::set_desired_copy_ratio(float value) {
  _internal_set_desired_copy_ratio(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions.desired_copy_ratio)
}

// optional bool allow_unsatisfied_fully_pipelined_prefetch = 3;
inline bool MemoryBoundLoopOptimizerOptions::_internal_has_allow_unsatisfied_fully_pipelined_prefetch() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool MemoryBoundLoopOptimizerOptions::has_allow_unsatisfied_fully_pipelined_prefetch() const {
  return _internal_has_allow_unsatisfied_fully_pipelined_prefetch();
}
inline void MemoryBoundLoopOptimizerOptions::clear_allow_unsatisfied_fully_pipelined_prefetch() {
  _impl_.allow_unsatisfied_fully_pipelined_prefetch_ = false;
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline bool MemoryBoundLoopOptimizerOptions::_internal_allow_unsatisfied_fully_pipelined_prefetch() const {
  return _impl_.allow_unsatisfied_fully_pipelined_prefetch_;
}
inline bool MemoryBoundLoopOptimizerOptions::allow_unsatisfied_fully_pipelined_prefetch() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions.allow_unsatisfied_fully_pipelined_prefetch)
  return _internal_allow_unsatisfied_fully_pipelined_prefetch();
}
inline void MemoryBoundLoopOptimizerOptions::_internal_set_allow_unsatisfied_fully_pipelined_prefetch(bool value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.allow_unsatisfied_fully_pipelined_prefetch_ = value;
}
inline void MemoryBoundLoopOptimizerOptions::set_allow_unsatisfied_fully_pipelined_prefetch(bool value) {
  _internal_set_allow_unsatisfied_fully_pipelined_prefetch(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions.allow_unsatisfied_fully_pipelined_prefetch)
}

// optional float min_num_iterations = 4;
inline bool MemoryBoundLoopOptimizerOptions::_internal_has_min_num_iterations() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool MemoryBoundLoopOptimizerOptions::has_min_num_iterations() const {
  return _internal_has_min_num_iterations();
}
inline void MemoryBoundLoopOptimizerOptions::clear_min_num_iterations() {
  _impl_.min_num_iterations_ = 0;
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline float MemoryBoundLoopOptimizerOptions::_internal_min_num_iterations() const {
  return _impl_.min_num_iterations_;
}
inline float MemoryBoundLoopOptimizerOptions::min_num_iterations() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions.min_num_iterations)
  return _internal_min_num_iterations();
}
inline void MemoryBoundLoopOptimizerOptions::_internal_set_min_num_iterations(float value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.min_num_iterations_ = value;
}
inline void MemoryBoundLoopOptimizerOptions::set_min_num_iterations(float value) {
  _internal_set_min_num_iterations(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.MemoryBoundLoopOptimizerOptions.min_num_iterations)
}

// -------------------------------------------------------------------

// TupleShapeIndex

// repeated int64 index = 1;
inline int TupleShapeIndex::_internal_index_size() const {
  return _impl_.index_.size();
}
inline int TupleShapeIndex::index_size() const {
  return _internal_index_size();
}
inline void TupleShapeIndex::clear_index() {
  _impl_.index_.Clear();
}
inline int64_t TupleShapeIndex::_internal_index(int index) const {
  return _impl_.index_.Get(index);
}
inline int64_t TupleShapeIndex::index(int index) const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.TupleShapeIndex.index)
  return _internal_index(index);
}
inline void TupleShapeIndex::set_index(int index, int64_t value) {
  _impl_.index_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.TupleShapeIndex.index)
}
inline void TupleShapeIndex::_internal_add_index(int64_t value) {
  _impl_.index_.Add(value);
}
inline void TupleShapeIndex::add_index(int64_t value) {
  _internal_add_index(value);
  // @@protoc_insertion_point(field_add:xla.memory_space_assignment.TupleShapeIndex.index)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TupleShapeIndex::_internal_index() const {
  return _impl_.index_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TupleShapeIndex::index() const {
  // @@protoc_insertion_point(field_list:xla.memory_space_assignment.TupleShapeIndex.index)
  return _internal_index();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TupleShapeIndex::_internal_mutable_index() {
  return &_impl_.index_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TupleShapeIndex::mutable_index() {
  // @@protoc_insertion_point(field_mutable_list:xla.memory_space_assignment.TupleShapeIndex.index)
  return _internal_mutable_index();
}

// -------------------------------------------------------------------

// HloOperandFilter

// optional string instruction_name_regex = 1;
inline bool HloOperandFilter::_internal_has_instruction_name_regex() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool HloOperandFilter::has_instruction_name_regex() const {
  return _internal_has_instruction_name_regex();
}
inline void HloOperandFilter::clear_instruction_name_regex() {
  _impl_.instruction_name_regex_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& HloOperandFilter::instruction_name_regex() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloOperandFilter.instruction_name_regex)
  return _internal_instruction_name_regex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloOperandFilter::set_instruction_name_regex(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.instruction_name_regex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.HloOperandFilter.instruction_name_regex)
}
inline std::string* HloOperandFilter::mutable_instruction_name_regex() {
  std::string* _s = _internal_mutable_instruction_name_regex();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.HloOperandFilter.instruction_name_regex)
  return _s;
}
inline const std::string& HloOperandFilter::_internal_instruction_name_regex() const {
  return _impl_.instruction_name_regex_.Get();
}
inline void HloOperandFilter::_internal_set_instruction_name_regex(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.instruction_name_regex_.Set(value, GetArenaForAllocation());
}
inline std::string* HloOperandFilter::_internal_mutable_instruction_name_regex() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.instruction_name_regex_.Mutable(GetArenaForAllocation());
}
inline std::string* HloOperandFilter::release_instruction_name_regex() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.HloOperandFilter.instruction_name_regex)
  if (!_internal_has_instruction_name_regex()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.instruction_name_regex_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.instruction_name_regex_.IsDefault()) {
    _impl_.instruction_name_regex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void HloOperandFilter::set_allocated_instruction_name_regex(std::string* instruction_name_regex) {
  if (instruction_name_regex != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.instruction_name_regex_.SetAllocated(instruction_name_regex, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.instruction_name_regex_.IsDefault()) {
    _impl_.instruction_name_regex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.HloOperandFilter.instruction_name_regex)
}

// optional int64 operand_number = 2;
inline bool HloOperandFilter::_internal_has_operand_number() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool HloOperandFilter::has_operand_number() const {
  return _internal_has_operand_number();
}
inline void HloOperandFilter::clear_operand_number() {
  _impl_.operand_number_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline int64_t HloOperandFilter::_internal_operand_number() const {
  return _impl_.operand_number_;
}
inline int64_t HloOperandFilter::operand_number() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloOperandFilter.operand_number)
  return _internal_operand_number();
}
inline void HloOperandFilter::_internal_set_operand_number(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.operand_number_ = value;
}
inline void HloOperandFilter::set_operand_number(int64_t value) {
  _internal_set_operand_number(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.HloOperandFilter.operand_number)
}

// optional int64 size_gte = 3;
inline bool HloOperandFilter::_internal_has_size_gte() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool HloOperandFilter::has_size_gte() const {
  return _internal_has_size_gte();
}
inline void HloOperandFilter::clear_size_gte() {
  _impl_.size_gte_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline int64_t HloOperandFilter::_internal_size_gte() const {
  return _impl_.size_gte_;
}
inline int64_t HloOperandFilter::size_gte() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloOperandFilter.size_gte)
  return _internal_size_gte();
}
inline void HloOperandFilter::_internal_set_size_gte(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.size_gte_ = value;
}
inline void HloOperandFilter::set_size_gte(int64_t value) {
  _internal_set_size_gte(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.HloOperandFilter.size_gte)
}

// optional int64 size_lte = 4;
inline bool HloOperandFilter::_internal_has_size_lte() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool HloOperandFilter::has_size_lte() const {
  return _internal_has_size_lte();
}
inline void HloOperandFilter::clear_size_lte() {
  _impl_.size_lte_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline int64_t HloOperandFilter::_internal_size_lte() const {
  return _impl_.size_lte_;
}
inline int64_t HloOperandFilter::size_lte() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloOperandFilter.size_lte)
  return _internal_size_lte();
}
inline void HloOperandFilter::_internal_set_size_lte(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000020u;
  _impl_.size_lte_ = value;
}
inline void HloOperandFilter::set_size_lte(int64_t value) {
  _internal_set_size_lte(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.HloOperandFilter.size_lte)
}

// optional .xla.memory_space_assignment.TupleShapeIndex tuple_index = 5;
inline bool HloOperandFilter::_internal_has_tuple_index() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.tuple_index_ != nullptr);
  return value;
}
inline bool HloOperandFilter::has_tuple_index() const {
  return _internal_has_tuple_index();
}
inline void HloOperandFilter::clear_tuple_index() {
  if (_impl_.tuple_index_ != nullptr) _impl_.tuple_index_->Clear();
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline const ::xla::memory_space_assignment::TupleShapeIndex& HloOperandFilter::_internal_tuple_index() const {
  const ::xla::memory_space_assignment::TupleShapeIndex* p = _impl_.tuple_index_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::memory_space_assignment::TupleShapeIndex&>(
      ::xla::memory_space_assignment::_TupleShapeIndex_default_instance_);
}
inline const ::xla::memory_space_assignment::TupleShapeIndex& HloOperandFilter::tuple_index() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloOperandFilter.tuple_index)
  return _internal_tuple_index();
}
inline void HloOperandFilter::unsafe_arena_set_allocated_tuple_index(
    ::xla::memory_space_assignment::TupleShapeIndex* tuple_index) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tuple_index_);
  }
  _impl_.tuple_index_ = tuple_index;
  if (tuple_index) {
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.memory_space_assignment.HloOperandFilter.tuple_index)
}
inline ::xla::memory_space_assignment::TupleShapeIndex* HloOperandFilter::release_tuple_index() {
  _impl_._has_bits_[0] &= ~0x00000004u;
  ::xla::memory_space_assignment::TupleShapeIndex* temp = _impl_.tuple_index_;
  _impl_.tuple_index_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::memory_space_assignment::TupleShapeIndex* HloOperandFilter::unsafe_arena_release_tuple_index() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.HloOperandFilter.tuple_index)
  _impl_._has_bits_[0] &= ~0x00000004u;
  ::xla::memory_space_assignment::TupleShapeIndex* temp = _impl_.tuple_index_;
  _impl_.tuple_index_ = nullptr;
  return temp;
}
inline ::xla::memory_space_assignment::TupleShapeIndex* HloOperandFilter::_internal_mutable_tuple_index() {
  _impl_._has_bits_[0] |= 0x00000004u;
  if (_impl_.tuple_index_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::memory_space_assignment::TupleShapeIndex>(GetArenaForAllocation());
    _impl_.tuple_index_ = p;
  }
  return _impl_.tuple_index_;
}
inline ::xla::memory_space_assignment::TupleShapeIndex* HloOperandFilter::mutable_tuple_index() {
  ::xla::memory_space_assignment::TupleShapeIndex* _msg = _internal_mutable_tuple_index();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.HloOperandFilter.tuple_index)
  return _msg;
}
inline void HloOperandFilter::set_allocated_tuple_index(::xla::memory_space_assignment::TupleShapeIndex* tuple_index) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.tuple_index_;
  }
  if (tuple_index) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(tuple_index);
    if (message_arena != submessage_arena) {
      tuple_index = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tuple_index, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  _impl_.tuple_index_ = tuple_index;
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.HloOperandFilter.tuple_index)
}

// optional string instruction_regex = 6;
inline bool HloOperandFilter::_internal_has_instruction_regex() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool HloOperandFilter::has_instruction_regex() const {
  return _internal_has_instruction_regex();
}
inline void HloOperandFilter::clear_instruction_regex() {
  _impl_.instruction_regex_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& HloOperandFilter::instruction_regex() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloOperandFilter.instruction_regex)
  return _internal_instruction_regex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloOperandFilter::set_instruction_regex(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.instruction_regex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.HloOperandFilter.instruction_regex)
}
inline std::string* HloOperandFilter::mutable_instruction_regex() {
  std::string* _s = _internal_mutable_instruction_regex();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.HloOperandFilter.instruction_regex)
  return _s;
}
inline const std::string& HloOperandFilter::_internal_instruction_regex() const {
  return _impl_.instruction_regex_.Get();
}
inline void HloOperandFilter::_internal_set_instruction_regex(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.instruction_regex_.Set(value, GetArenaForAllocation());
}
inline std::string* HloOperandFilter::_internal_mutable_instruction_regex() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.instruction_regex_.Mutable(GetArenaForAllocation());
}
inline std::string* HloOperandFilter::release_instruction_regex() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.HloOperandFilter.instruction_regex)
  if (!_internal_has_instruction_regex()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.instruction_regex_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.instruction_regex_.IsDefault()) {
    _impl_.instruction_regex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void HloOperandFilter::set_allocated_instruction_regex(std::string* instruction_regex) {
  if (instruction_regex != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.instruction_regex_.SetAllocated(instruction_regex, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.instruction_regex_.IsDefault()) {
    _impl_.instruction_regex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.HloOperandFilter.instruction_regex)
}

// -------------------------------------------------------------------

// PreferredPrefetchOverrideOptions

// float prefetch_eagerness = 1;
inline bool PreferredPrefetchOverrideOptions::_internal_has_prefetch_eagerness() const {
  return options_case() == kPrefetchEagerness;
}
inline bool PreferredPrefetchOverrideOptions::has_prefetch_eagerness() const {
  return _internal_has_prefetch_eagerness();
}
inline void PreferredPrefetchOverrideOptions::set_has_prefetch_eagerness() {
  _impl_._oneof_case_[0] = kPrefetchEagerness;
}
inline void PreferredPrefetchOverrideOptions::clear_prefetch_eagerness() {
  if (_internal_has_prefetch_eagerness()) {
    _impl_.options_.prefetch_eagerness_ = 0;
    clear_has_options();
  }
}
inline float PreferredPrefetchOverrideOptions::_internal_prefetch_eagerness() const {
  if (_internal_has_prefetch_eagerness()) {
    return _impl_.options_.prefetch_eagerness_;
  }
  return 0;
}
inline void PreferredPrefetchOverrideOptions::_internal_set_prefetch_eagerness(float value) {
  if (!_internal_has_prefetch_eagerness()) {
    clear_options();
    set_has_prefetch_eagerness();
  }
  _impl_.options_.prefetch_eagerness_ = value;
}
inline float PreferredPrefetchOverrideOptions::prefetch_eagerness() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.prefetch_eagerness)
  return _internal_prefetch_eagerness();
}
inline void PreferredPrefetchOverrideOptions::set_prefetch_eagerness(float value) {
  _internal_set_prefetch_eagerness(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.prefetch_eagerness)
}

// .xla.memory_space_assignment.HloPositionMatcher after_instruction = 4;
inline bool PreferredPrefetchOverrideOptions::_internal_has_after_instruction() const {
  return options_case() == kAfterInstruction;
}
inline bool PreferredPrefetchOverrideOptions::has_after_instruction() const {
  return _internal_has_after_instruction();
}
inline void PreferredPrefetchOverrideOptions::set_has_after_instruction() {
  _impl_._oneof_case_[0] = kAfterInstruction;
}
inline void PreferredPrefetchOverrideOptions::clear_after_instruction() {
  if (_internal_has_after_instruction()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.options_.after_instruction_;
    }
    clear_has_options();
  }
}
inline ::xla::memory_space_assignment::HloPositionMatcher* PreferredPrefetchOverrideOptions::release_after_instruction() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.after_instruction)
  if (_internal_has_after_instruction()) {
    clear_has_options();
    ::xla::memory_space_assignment::HloPositionMatcher* temp = _impl_.options_.after_instruction_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.options_.after_instruction_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::memory_space_assignment::HloPositionMatcher& PreferredPrefetchOverrideOptions::_internal_after_instruction() const {
  return _internal_has_after_instruction()
      ? *_impl_.options_.after_instruction_
      : reinterpret_cast< ::xla::memory_space_assignment::HloPositionMatcher&>(::xla::memory_space_assignment::_HloPositionMatcher_default_instance_);
}
inline const ::xla::memory_space_assignment::HloPositionMatcher& PreferredPrefetchOverrideOptions::after_instruction() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.after_instruction)
  return _internal_after_instruction();
}
inline ::xla::memory_space_assignment::HloPositionMatcher* PreferredPrefetchOverrideOptions::unsafe_arena_release_after_instruction() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.after_instruction)
  if (_internal_has_after_instruction()) {
    clear_has_options();
    ::xla::memory_space_assignment::HloPositionMatcher* temp = _impl_.options_.after_instruction_;
    _impl_.options_.after_instruction_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void PreferredPrefetchOverrideOptions::unsafe_arena_set_allocated_after_instruction(::xla::memory_space_assignment::HloPositionMatcher* after_instruction) {
  clear_options();
  if (after_instruction) {
    set_has_after_instruction();
    _impl_.options_.after_instruction_ = after_instruction;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.after_instruction)
}
inline ::xla::memory_space_assignment::HloPositionMatcher* PreferredPrefetchOverrideOptions::_internal_mutable_after_instruction() {
  if (!_internal_has_after_instruction()) {
    clear_options();
    set_has_after_instruction();
    _impl_.options_.after_instruction_ = CreateMaybeMessage< ::xla::memory_space_assignment::HloPositionMatcher >(GetArenaForAllocation());
  }
  return _impl_.options_.after_instruction_;
}
inline ::xla::memory_space_assignment::HloPositionMatcher* PreferredPrefetchOverrideOptions::mutable_after_instruction() {
  ::xla::memory_space_assignment::HloPositionMatcher* _msg = _internal_mutable_after_instruction();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.after_instruction)
  return _msg;
}

// .xla.memory_space_assignment.HloPositionMatcher before_instruction = 5;
inline bool PreferredPrefetchOverrideOptions::_internal_has_before_instruction() const {
  return options_case() == kBeforeInstruction;
}
inline bool PreferredPrefetchOverrideOptions::has_before_instruction() const {
  return _internal_has_before_instruction();
}
inline void PreferredPrefetchOverrideOptions::set_has_before_instruction() {
  _impl_._oneof_case_[0] = kBeforeInstruction;
}
inline void PreferredPrefetchOverrideOptions::clear_before_instruction() {
  if (_internal_has_before_instruction()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.options_.before_instruction_;
    }
    clear_has_options();
  }
}
inline ::xla::memory_space_assignment::HloPositionMatcher* PreferredPrefetchOverrideOptions::release_before_instruction() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.before_instruction)
  if (_internal_has_before_instruction()) {
    clear_has_options();
    ::xla::memory_space_assignment::HloPositionMatcher* temp = _impl_.options_.before_instruction_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.options_.before_instruction_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::memory_space_assignment::HloPositionMatcher& PreferredPrefetchOverrideOptions::_internal_before_instruction() const {
  return _internal_has_before_instruction()
      ? *_impl_.options_.before_instruction_
      : reinterpret_cast< ::xla::memory_space_assignment::HloPositionMatcher&>(::xla::memory_space_assignment::_HloPositionMatcher_default_instance_);
}
inline const ::xla::memory_space_assignment::HloPositionMatcher& PreferredPrefetchOverrideOptions::before_instruction() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.before_instruction)
  return _internal_before_instruction();
}
inline ::xla::memory_space_assignment::HloPositionMatcher* PreferredPrefetchOverrideOptions::unsafe_arena_release_before_instruction() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.before_instruction)
  if (_internal_has_before_instruction()) {
    clear_has_options();
    ::xla::memory_space_assignment::HloPositionMatcher* temp = _impl_.options_.before_instruction_;
    _impl_.options_.before_instruction_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void PreferredPrefetchOverrideOptions::unsafe_arena_set_allocated_before_instruction(::xla::memory_space_assignment::HloPositionMatcher* before_instruction) {
  clear_options();
  if (before_instruction) {
    set_has_before_instruction();
    _impl_.options_.before_instruction_ = before_instruction;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.before_instruction)
}
inline ::xla::memory_space_assignment::HloPositionMatcher* PreferredPrefetchOverrideOptions::_internal_mutable_before_instruction() {
  if (!_internal_has_before_instruction()) {
    clear_options();
    set_has_before_instruction();
    _impl_.options_.before_instruction_ = CreateMaybeMessage< ::xla::memory_space_assignment::HloPositionMatcher >(GetArenaForAllocation());
  }
  return _impl_.options_.before_instruction_;
}
inline ::xla::memory_space_assignment::HloPositionMatcher* PreferredPrefetchOverrideOptions::mutable_before_instruction() {
  ::xla::memory_space_assignment::HloPositionMatcher* _msg = _internal_mutable_before_instruction();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.PreferredPrefetchOverrideOptions.before_instruction)
  return _msg;
}

inline bool PreferredPrefetchOverrideOptions::has_options() const {
  return options_case() != OPTIONS_NOT_SET;
}
inline void PreferredPrefetchOverrideOptions::clear_has_options() {
  _impl_._oneof_case_[0] = OPTIONS_NOT_SET;
}
inline PreferredPrefetchOverrideOptions::OptionsCase PreferredPrefetchOverrideOptions::options_case() const {
  return PreferredPrefetchOverrideOptions::OptionsCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// PreferredPrefetchOverride

// optional .xla.memory_space_assignment.HloOperandFilter hlo_operand_filter = 1;
inline bool PreferredPrefetchOverride::_internal_has_hlo_operand_filter() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.hlo_operand_filter_ != nullptr);
  return value;
}
inline bool PreferredPrefetchOverride::has_hlo_operand_filter() const {
  return _internal_has_hlo_operand_filter();
}
inline void PreferredPrefetchOverride::clear_hlo_operand_filter() {
  if (_impl_.hlo_operand_filter_ != nullptr) _impl_.hlo_operand_filter_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::xla::memory_space_assignment::HloOperandFilter& PreferredPrefetchOverride::_internal_hlo_operand_filter() const {
  const ::xla::memory_space_assignment::HloOperandFilter* p = _impl_.hlo_operand_filter_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::memory_space_assignment::HloOperandFilter&>(
      ::xla::memory_space_assignment::_HloOperandFilter_default_instance_);
}
inline const ::xla::memory_space_assignment::HloOperandFilter& PreferredPrefetchOverride::hlo_operand_filter() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.PreferredPrefetchOverride.hlo_operand_filter)
  return _internal_hlo_operand_filter();
}
inline void PreferredPrefetchOverride::unsafe_arena_set_allocated_hlo_operand_filter(
    ::xla::memory_space_assignment::HloOperandFilter* hlo_operand_filter) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hlo_operand_filter_);
  }
  _impl_.hlo_operand_filter_ = hlo_operand_filter;
  if (hlo_operand_filter) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.memory_space_assignment.PreferredPrefetchOverride.hlo_operand_filter)
}
inline ::xla::memory_space_assignment::HloOperandFilter* PreferredPrefetchOverride::release_hlo_operand_filter() {
  _impl_._has_bits_[0] &= ~0x00000001u;
  ::xla::memory_space_assignment::HloOperandFilter* temp = _impl_.hlo_operand_filter_;
  _impl_.hlo_operand_filter_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::memory_space_assignment::HloOperandFilter* PreferredPrefetchOverride::unsafe_arena_release_hlo_operand_filter() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.PreferredPrefetchOverride.hlo_operand_filter)
  _impl_._has_bits_[0] &= ~0x00000001u;
  ::xla::memory_space_assignment::HloOperandFilter* temp = _impl_.hlo_operand_filter_;
  _impl_.hlo_operand_filter_ = nullptr;
  return temp;
}
inline ::xla::memory_space_assignment::HloOperandFilter* PreferredPrefetchOverride::_internal_mutable_hlo_operand_filter() {
  _impl_._has_bits_[0] |= 0x00000001u;
  if (_impl_.hlo_operand_filter_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::memory_space_assignment::HloOperandFilter>(GetArenaForAllocation());
    _impl_.hlo_operand_filter_ = p;
  }
  return _impl_.hlo_operand_filter_;
}
inline ::xla::memory_space_assignment::HloOperandFilter* PreferredPrefetchOverride::mutable_hlo_operand_filter() {
  ::xla::memory_space_assignment::HloOperandFilter* _msg = _internal_mutable_hlo_operand_filter();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.PreferredPrefetchOverride.hlo_operand_filter)
  return _msg;
}
inline void PreferredPrefetchOverride::set_allocated_hlo_operand_filter(::xla::memory_space_assignment::HloOperandFilter* hlo_operand_filter) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.hlo_operand_filter_;
  }
  if (hlo_operand_filter) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(hlo_operand_filter);
    if (message_arena != submessage_arena) {
      hlo_operand_filter = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hlo_operand_filter, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.hlo_operand_filter_ = hlo_operand_filter;
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.PreferredPrefetchOverride.hlo_operand_filter)
}

// optional .xla.memory_space_assignment.PreferredPrefetchOverrideOptions override_options = 2;
inline bool PreferredPrefetchOverride::_internal_has_override_options() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.override_options_ != nullptr);
  return value;
}
inline bool PreferredPrefetchOverride::has_override_options() const {
  return _internal_has_override_options();
}
inline void PreferredPrefetchOverride::clear_override_options() {
  if (_impl_.override_options_ != nullptr) _impl_.override_options_->Clear();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions& PreferredPrefetchOverride::_internal_override_options() const {
  const ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* p = _impl_.override_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions&>(
      ::xla::memory_space_assignment::_PreferredPrefetchOverrideOptions_default_instance_);
}
inline const ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions& PreferredPrefetchOverride::override_options() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.PreferredPrefetchOverride.override_options)
  return _internal_override_options();
}
inline void PreferredPrefetchOverride::unsafe_arena_set_allocated_override_options(
    ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* override_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.override_options_);
  }
  _impl_.override_options_ = override_options;
  if (override_options) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.memory_space_assignment.PreferredPrefetchOverride.override_options)
}
inline ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* PreferredPrefetchOverride::release_override_options() {
  _impl_._has_bits_[0] &= ~0x00000002u;
  ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* temp = _impl_.override_options_;
  _impl_.override_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* PreferredPrefetchOverride::unsafe_arena_release_override_options() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.PreferredPrefetchOverride.override_options)
  _impl_._has_bits_[0] &= ~0x00000002u;
  ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* temp = _impl_.override_options_;
  _impl_.override_options_ = nullptr;
  return temp;
}
inline ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* PreferredPrefetchOverride::_internal_mutable_override_options() {
  _impl_._has_bits_[0] |= 0x00000002u;
  if (_impl_.override_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::memory_space_assignment::PreferredPrefetchOverrideOptions>(GetArenaForAllocation());
    _impl_.override_options_ = p;
  }
  return _impl_.override_options_;
}
inline ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* PreferredPrefetchOverride::mutable_override_options() {
  ::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* _msg = _internal_mutable_override_options();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.PreferredPrefetchOverride.override_options)
  return _msg;
}
inline void PreferredPrefetchOverride::set_allocated_override_options(::xla::memory_space_assignment::PreferredPrefetchOverrideOptions* override_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.override_options_;
  }
  if (override_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(override_options);
    if (message_arena != submessage_arena) {
      override_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, override_options, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.override_options_ = override_options;
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.PreferredPrefetchOverride.override_options)
}

// -------------------------------------------------------------------

// PreferredPrefetchOverrides

// repeated .xla.memory_space_assignment.PreferredPrefetchOverride overrides = 1;
inline int PreferredPrefetchOverrides::_internal_overrides_size() const {
  return _impl_.overrides_.size();
}
inline int PreferredPrefetchOverrides::overrides_size() const {
  return _internal_overrides_size();
}
inline void PreferredPrefetchOverrides::clear_overrides() {
  _impl_.overrides_.Clear();
}
inline ::xla::memory_space_assignment::PreferredPrefetchOverride* PreferredPrefetchOverrides::mutable_overrides(int index) {
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.PreferredPrefetchOverrides.overrides)
  return _impl_.overrides_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::PreferredPrefetchOverride >*
PreferredPrefetchOverrides::mutable_overrides() {
  // @@protoc_insertion_point(field_mutable_list:xla.memory_space_assignment.PreferredPrefetchOverrides.overrides)
  return &_impl_.overrides_;
}
inline const ::xla::memory_space_assignment::PreferredPrefetchOverride& PreferredPrefetchOverrides::_internal_overrides(int index) const {
  return _impl_.overrides_.Get(index);
}
inline const ::xla::memory_space_assignment::PreferredPrefetchOverride& PreferredPrefetchOverrides::overrides(int index) const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.PreferredPrefetchOverrides.overrides)
  return _internal_overrides(index);
}
inline ::xla::memory_space_assignment::PreferredPrefetchOverride* PreferredPrefetchOverrides::_internal_add_overrides() {
  return _impl_.overrides_.Add();
}
inline ::xla::memory_space_assignment::PreferredPrefetchOverride* PreferredPrefetchOverrides::add_overrides() {
  ::xla::memory_space_assignment::PreferredPrefetchOverride* _add = _internal_add_overrides();
  // @@protoc_insertion_point(field_add:xla.memory_space_assignment.PreferredPrefetchOverrides.overrides)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::PreferredPrefetchOverride >&
PreferredPrefetchOverrides::overrides() const {
  // @@protoc_insertion_point(field_list:xla.memory_space_assignment.PreferredPrefetchOverrides.overrides)
  return _impl_.overrides_;
}

// -------------------------------------------------------------------

// HloPositionMatcher

// optional string instruction_regex = 1;
inline bool HloPositionMatcher::_internal_has_instruction_regex() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool HloPositionMatcher::has_instruction_regex() const {
  return _internal_has_instruction_regex();
}
inline void HloPositionMatcher::clear_instruction_regex() {
  _impl_.instruction_regex_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& HloPositionMatcher::instruction_regex() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloPositionMatcher.instruction_regex)
  return _internal_instruction_regex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloPositionMatcher::set_instruction_regex(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.instruction_regex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.HloPositionMatcher.instruction_regex)
}
inline std::string* HloPositionMatcher::mutable_instruction_regex() {
  std::string* _s = _internal_mutable_instruction_regex();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.HloPositionMatcher.instruction_regex)
  return _s;
}
inline const std::string& HloPositionMatcher::_internal_instruction_regex() const {
  return _impl_.instruction_regex_.Get();
}
inline void HloPositionMatcher::_internal_set_instruction_regex(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.instruction_regex_.Set(value, GetArenaForAllocation());
}
inline std::string* HloPositionMatcher::_internal_mutable_instruction_regex() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.instruction_regex_.Mutable(GetArenaForAllocation());
}
inline std::string* HloPositionMatcher::release_instruction_regex() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.HloPositionMatcher.instruction_regex)
  if (!_internal_has_instruction_regex()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.instruction_regex_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.instruction_regex_.IsDefault()) {
    _impl_.instruction_regex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void HloPositionMatcher::set_allocated_instruction_regex(std::string* instruction_regex) {
  if (instruction_regex != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.instruction_regex_.SetAllocated(instruction_regex, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.instruction_regex_.IsDefault()) {
    _impl_.instruction_regex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.HloPositionMatcher.instruction_regex)
}

// optional string instruction_name_regex = 2;
inline bool HloPositionMatcher::_internal_has_instruction_name_regex() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool HloPositionMatcher::has_instruction_name_regex() const {
  return _internal_has_instruction_name_regex();
}
inline void HloPositionMatcher::clear_instruction_name_regex() {
  _impl_.instruction_name_regex_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& HloPositionMatcher::instruction_name_regex() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloPositionMatcher.instruction_name_regex)
  return _internal_instruction_name_regex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloPositionMatcher::set_instruction_name_regex(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.instruction_name_regex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.HloPositionMatcher.instruction_name_regex)
}
inline std::string* HloPositionMatcher::mutable_instruction_name_regex() {
  std::string* _s = _internal_mutable_instruction_name_regex();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.HloPositionMatcher.instruction_name_regex)
  return _s;
}
inline const std::string& HloPositionMatcher::_internal_instruction_name_regex() const {
  return _impl_.instruction_name_regex_.Get();
}
inline void HloPositionMatcher::_internal_set_instruction_name_regex(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.instruction_name_regex_.Set(value, GetArenaForAllocation());
}
inline std::string* HloPositionMatcher::_internal_mutable_instruction_name_regex() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.instruction_name_regex_.Mutable(GetArenaForAllocation());
}
inline std::string* HloPositionMatcher::release_instruction_name_regex() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.HloPositionMatcher.instruction_name_regex)
  if (!_internal_has_instruction_name_regex()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.instruction_name_regex_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.instruction_name_regex_.IsDefault()) {
    _impl_.instruction_name_regex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void HloPositionMatcher::set_allocated_instruction_name_regex(std::string* instruction_name_regex) {
  if (instruction_name_regex != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.instruction_name_regex_.SetAllocated(instruction_name_regex, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.instruction_name_regex_.IsDefault()) {
    _impl_.instruction_name_regex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.HloPositionMatcher.instruction_name_regex)
}

// optional .xla.memory_space_assignment.TupleShapeIndex tuple_index = 3;
inline bool HloPositionMatcher::_internal_has_tuple_index() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.tuple_index_ != nullptr);
  return value;
}
inline bool HloPositionMatcher::has_tuple_index() const {
  return _internal_has_tuple_index();
}
inline void HloPositionMatcher::clear_tuple_index() {
  if (_impl_.tuple_index_ != nullptr) _impl_.tuple_index_->Clear();
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline const ::xla::memory_space_assignment::TupleShapeIndex& HloPositionMatcher::_internal_tuple_index() const {
  const ::xla::memory_space_assignment::TupleShapeIndex* p = _impl_.tuple_index_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::memory_space_assignment::TupleShapeIndex&>(
      ::xla::memory_space_assignment::_TupleShapeIndex_default_instance_);
}
inline const ::xla::memory_space_assignment::TupleShapeIndex& HloPositionMatcher::tuple_index() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloPositionMatcher.tuple_index)
  return _internal_tuple_index();
}
inline void HloPositionMatcher::unsafe_arena_set_allocated_tuple_index(
    ::xla::memory_space_assignment::TupleShapeIndex* tuple_index) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tuple_index_);
  }
  _impl_.tuple_index_ = tuple_index;
  if (tuple_index) {
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.memory_space_assignment.HloPositionMatcher.tuple_index)
}
inline ::xla::memory_space_assignment::TupleShapeIndex* HloPositionMatcher::release_tuple_index() {
  _impl_._has_bits_[0] &= ~0x00000004u;
  ::xla::memory_space_assignment::TupleShapeIndex* temp = _impl_.tuple_index_;
  _impl_.tuple_index_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::memory_space_assignment::TupleShapeIndex* HloPositionMatcher::unsafe_arena_release_tuple_index() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.HloPositionMatcher.tuple_index)
  _impl_._has_bits_[0] &= ~0x00000004u;
  ::xla::memory_space_assignment::TupleShapeIndex* temp = _impl_.tuple_index_;
  _impl_.tuple_index_ = nullptr;
  return temp;
}
inline ::xla::memory_space_assignment::TupleShapeIndex* HloPositionMatcher::_internal_mutable_tuple_index() {
  _impl_._has_bits_[0] |= 0x00000004u;
  if (_impl_.tuple_index_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::memory_space_assignment::TupleShapeIndex>(GetArenaForAllocation());
    _impl_.tuple_index_ = p;
  }
  return _impl_.tuple_index_;
}
inline ::xla::memory_space_assignment::TupleShapeIndex* HloPositionMatcher::mutable_tuple_index() {
  ::xla::memory_space_assignment::TupleShapeIndex* _msg = _internal_mutable_tuple_index();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.HloPositionMatcher.tuple_index)
  return _msg;
}
inline void HloPositionMatcher::set_allocated_tuple_index(::xla::memory_space_assignment::TupleShapeIndex* tuple_index) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.tuple_index_;
  }
  if (tuple_index) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(tuple_index);
    if (message_arena != submessage_arena) {
      tuple_index = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tuple_index, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  _impl_.tuple_index_ = tuple_index;
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.HloPositionMatcher.tuple_index)
}

// optional int64 size_gte = 4;
inline bool HloPositionMatcher::_internal_has_size_gte() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool HloPositionMatcher::has_size_gte() const {
  return _internal_has_size_gte();
}
inline void HloPositionMatcher::clear_size_gte() {
  _impl_.size_gte_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline int64_t HloPositionMatcher::_internal_size_gte() const {
  return _impl_.size_gte_;
}
inline int64_t HloPositionMatcher::size_gte() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloPositionMatcher.size_gte)
  return _internal_size_gte();
}
inline void HloPositionMatcher::_internal_set_size_gte(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.size_gte_ = value;
}
inline void HloPositionMatcher::set_size_gte(int64_t value) {
  _internal_set_size_gte(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.HloPositionMatcher.size_gte)
}

// optional int64 size_lte = 5;
inline bool HloPositionMatcher::_internal_has_size_lte() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool HloPositionMatcher::has_size_lte() const {
  return _internal_has_size_lte();
}
inline void HloPositionMatcher::clear_size_lte() {
  _impl_.size_lte_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline int64_t HloPositionMatcher::_internal_size_lte() const {
  return _impl_.size_lte_;
}
inline int64_t HloPositionMatcher::size_lte() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloPositionMatcher.size_lte)
  return _internal_size_lte();
}
inline void HloPositionMatcher::_internal_set_size_lte(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000020u;
  _impl_.size_lte_ = value;
}
inline void HloPositionMatcher::set_size_lte(int64_t value) {
  _internal_set_size_lte(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.HloPositionMatcher.size_lte)
}

// optional .xla.memory_space_assignment.HloOperandFilter hlo_use_filter = 6;
inline bool HloPositionMatcher::_internal_has_hlo_use_filter() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.hlo_use_filter_ != nullptr);
  return value;
}
inline bool HloPositionMatcher::has_hlo_use_filter() const {
  return _internal_has_hlo_use_filter();
}
inline void HloPositionMatcher::clear_hlo_use_filter() {
  if (_impl_.hlo_use_filter_ != nullptr) _impl_.hlo_use_filter_->Clear();
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline const ::xla::memory_space_assignment::HloOperandFilter& HloPositionMatcher::_internal_hlo_use_filter() const {
  const ::xla::memory_space_assignment::HloOperandFilter* p = _impl_.hlo_use_filter_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::memory_space_assignment::HloOperandFilter&>(
      ::xla::memory_space_assignment::_HloOperandFilter_default_instance_);
}
inline const ::xla::memory_space_assignment::HloOperandFilter& HloPositionMatcher::hlo_use_filter() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.HloPositionMatcher.hlo_use_filter)
  return _internal_hlo_use_filter();
}
inline void HloPositionMatcher::unsafe_arena_set_allocated_hlo_use_filter(
    ::xla::memory_space_assignment::HloOperandFilter* hlo_use_filter) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hlo_use_filter_);
  }
  _impl_.hlo_use_filter_ = hlo_use_filter;
  if (hlo_use_filter) {
    _impl_._has_bits_[0] |= 0x00000008u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000008u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.memory_space_assignment.HloPositionMatcher.hlo_use_filter)
}
inline ::xla::memory_space_assignment::HloOperandFilter* HloPositionMatcher::release_hlo_use_filter() {
  _impl_._has_bits_[0] &= ~0x00000008u;
  ::xla::memory_space_assignment::HloOperandFilter* temp = _impl_.hlo_use_filter_;
  _impl_.hlo_use_filter_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::memory_space_assignment::HloOperandFilter* HloPositionMatcher::unsafe_arena_release_hlo_use_filter() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.HloPositionMatcher.hlo_use_filter)
  _impl_._has_bits_[0] &= ~0x00000008u;
  ::xla::memory_space_assignment::HloOperandFilter* temp = _impl_.hlo_use_filter_;
  _impl_.hlo_use_filter_ = nullptr;
  return temp;
}
inline ::xla::memory_space_assignment::HloOperandFilter* HloPositionMatcher::_internal_mutable_hlo_use_filter() {
  _impl_._has_bits_[0] |= 0x00000008u;
  if (_impl_.hlo_use_filter_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::memory_space_assignment::HloOperandFilter>(GetArenaForAllocation());
    _impl_.hlo_use_filter_ = p;
  }
  return _impl_.hlo_use_filter_;
}
inline ::xla::memory_space_assignment::HloOperandFilter* HloPositionMatcher::mutable_hlo_use_filter() {
  ::xla::memory_space_assignment::HloOperandFilter* _msg = _internal_mutable_hlo_use_filter();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.HloPositionMatcher.hlo_use_filter)
  return _msg;
}
inline void HloPositionMatcher::set_allocated_hlo_use_filter(::xla::memory_space_assignment::HloOperandFilter* hlo_use_filter) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.hlo_use_filter_;
  }
  if (hlo_use_filter) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(hlo_use_filter);
    if (message_arena != submessage_arena) {
      hlo_use_filter = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hlo_use_filter, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000008u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000008u;
  }
  _impl_.hlo_use_filter_ = hlo_use_filter;
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.HloPositionMatcher.hlo_use_filter)
}

// -------------------------------------------------------------------

// MsaSortOrderOverrideOptions

// bool assign_first = 1;
inline bool MsaSortOrderOverrideOptions::_internal_has_assign_first() const {
  return options_case() == kAssignFirst;
}
inline bool MsaSortOrderOverrideOptions::has_assign_first() const {
  return _internal_has_assign_first();
}
inline void MsaSortOrderOverrideOptions::set_has_assign_first() {
  _impl_._oneof_case_[0] = kAssignFirst;
}
inline void MsaSortOrderOverrideOptions::clear_assign_first() {
  if (_internal_has_assign_first()) {
    _impl_.options_.assign_first_ = false;
    clear_has_options();
  }
}
inline bool MsaSortOrderOverrideOptions::_internal_assign_first() const {
  if (_internal_has_assign_first()) {
    return _impl_.options_.assign_first_;
  }
  return false;
}
inline void MsaSortOrderOverrideOptions::_internal_set_assign_first(bool value) {
  if (!_internal_has_assign_first()) {
    clear_options();
    set_has_assign_first();
  }
  _impl_.options_.assign_first_ = value;
}
inline bool MsaSortOrderOverrideOptions::assign_first() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MsaSortOrderOverrideOptions.assign_first)
  return _internal_assign_first();
}
inline void MsaSortOrderOverrideOptions::set_assign_first(bool value) {
  _internal_set_assign_first(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.MsaSortOrderOverrideOptions.assign_first)
}

// bool assign_last = 2;
inline bool MsaSortOrderOverrideOptions::_internal_has_assign_last() const {
  return options_case() == kAssignLast;
}
inline bool MsaSortOrderOverrideOptions::has_assign_last() const {
  return _internal_has_assign_last();
}
inline void MsaSortOrderOverrideOptions::set_has_assign_last() {
  _impl_._oneof_case_[0] = kAssignLast;
}
inline void MsaSortOrderOverrideOptions::clear_assign_last() {
  if (_internal_has_assign_last()) {
    _impl_.options_.assign_last_ = false;
    clear_has_options();
  }
}
inline bool MsaSortOrderOverrideOptions::_internal_assign_last() const {
  if (_internal_has_assign_last()) {
    return _impl_.options_.assign_last_;
  }
  return false;
}
inline void MsaSortOrderOverrideOptions::_internal_set_assign_last(bool value) {
  if (!_internal_has_assign_last()) {
    clear_options();
    set_has_assign_last();
  }
  _impl_.options_.assign_last_ = value;
}
inline bool MsaSortOrderOverrideOptions::assign_last() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MsaSortOrderOverrideOptions.assign_last)
  return _internal_assign_last();
}
inline void MsaSortOrderOverrideOptions::set_assign_last(bool value) {
  _internal_set_assign_last(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.MsaSortOrderOverrideOptions.assign_last)
}

inline bool MsaSortOrderOverrideOptions::has_options() const {
  return options_case() != OPTIONS_NOT_SET;
}
inline void MsaSortOrderOverrideOptions::clear_has_options() {
  _impl_._oneof_case_[0] = OPTIONS_NOT_SET;
}
inline MsaSortOrderOverrideOptions::OptionsCase MsaSortOrderOverrideOptions::options_case() const {
  return MsaSortOrderOverrideOptions::OptionsCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// MsaSortOrderOverride

// optional .xla.memory_space_assignment.HloPositionMatcher hlo_position_matcher = 1;
inline bool MsaSortOrderOverride::_internal_has_hlo_position_matcher() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.hlo_position_matcher_ != nullptr);
  return value;
}
inline bool MsaSortOrderOverride::has_hlo_position_matcher() const {
  return _internal_has_hlo_position_matcher();
}
inline void MsaSortOrderOverride::clear_hlo_position_matcher() {
  if (_impl_.hlo_position_matcher_ != nullptr) _impl_.hlo_position_matcher_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::xla::memory_space_assignment::HloPositionMatcher& MsaSortOrderOverride::_internal_hlo_position_matcher() const {
  const ::xla::memory_space_assignment::HloPositionMatcher* p = _impl_.hlo_position_matcher_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::memory_space_assignment::HloPositionMatcher&>(
      ::xla::memory_space_assignment::_HloPositionMatcher_default_instance_);
}
inline const ::xla::memory_space_assignment::HloPositionMatcher& MsaSortOrderOverride::hlo_position_matcher() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MsaSortOrderOverride.hlo_position_matcher)
  return _internal_hlo_position_matcher();
}
inline void MsaSortOrderOverride::unsafe_arena_set_allocated_hlo_position_matcher(
    ::xla::memory_space_assignment::HloPositionMatcher* hlo_position_matcher) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hlo_position_matcher_);
  }
  _impl_.hlo_position_matcher_ = hlo_position_matcher;
  if (hlo_position_matcher) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.memory_space_assignment.MsaSortOrderOverride.hlo_position_matcher)
}
inline ::xla::memory_space_assignment::HloPositionMatcher* MsaSortOrderOverride::release_hlo_position_matcher() {
  _impl_._has_bits_[0] &= ~0x00000001u;
  ::xla::memory_space_assignment::HloPositionMatcher* temp = _impl_.hlo_position_matcher_;
  _impl_.hlo_position_matcher_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::memory_space_assignment::HloPositionMatcher* MsaSortOrderOverride::unsafe_arena_release_hlo_position_matcher() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.MsaSortOrderOverride.hlo_position_matcher)
  _impl_._has_bits_[0] &= ~0x00000001u;
  ::xla::memory_space_assignment::HloPositionMatcher* temp = _impl_.hlo_position_matcher_;
  _impl_.hlo_position_matcher_ = nullptr;
  return temp;
}
inline ::xla::memory_space_assignment::HloPositionMatcher* MsaSortOrderOverride::_internal_mutable_hlo_position_matcher() {
  _impl_._has_bits_[0] |= 0x00000001u;
  if (_impl_.hlo_position_matcher_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::memory_space_assignment::HloPositionMatcher>(GetArenaForAllocation());
    _impl_.hlo_position_matcher_ = p;
  }
  return _impl_.hlo_position_matcher_;
}
inline ::xla::memory_space_assignment::HloPositionMatcher* MsaSortOrderOverride::mutable_hlo_position_matcher() {
  ::xla::memory_space_assignment::HloPositionMatcher* _msg = _internal_mutable_hlo_position_matcher();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.MsaSortOrderOverride.hlo_position_matcher)
  return _msg;
}
inline void MsaSortOrderOverride::set_allocated_hlo_position_matcher(::xla::memory_space_assignment::HloPositionMatcher* hlo_position_matcher) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.hlo_position_matcher_;
  }
  if (hlo_position_matcher) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(hlo_position_matcher);
    if (message_arena != submessage_arena) {
      hlo_position_matcher = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hlo_position_matcher, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.hlo_position_matcher_ = hlo_position_matcher;
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.MsaSortOrderOverride.hlo_position_matcher)
}

// optional .xla.memory_space_assignment.MsaSortOrderOverrideOptions override_options = 2;
inline bool MsaSortOrderOverride::_internal_has_override_options() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.override_options_ != nullptr);
  return value;
}
inline bool MsaSortOrderOverride::has_override_options() const {
  return _internal_has_override_options();
}
inline void MsaSortOrderOverride::clear_override_options() {
  if (_impl_.override_options_ != nullptr) _impl_.override_options_->Clear();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const ::xla::memory_space_assignment::MsaSortOrderOverrideOptions& MsaSortOrderOverride::_internal_override_options() const {
  const ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* p = _impl_.override_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::memory_space_assignment::MsaSortOrderOverrideOptions&>(
      ::xla::memory_space_assignment::_MsaSortOrderOverrideOptions_default_instance_);
}
inline const ::xla::memory_space_assignment::MsaSortOrderOverrideOptions& MsaSortOrderOverride::override_options() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MsaSortOrderOverride.override_options)
  return _internal_override_options();
}
inline void MsaSortOrderOverride::unsafe_arena_set_allocated_override_options(
    ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* override_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.override_options_);
  }
  _impl_.override_options_ = override_options;
  if (override_options) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.memory_space_assignment.MsaSortOrderOverride.override_options)
}
inline ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* MsaSortOrderOverride::release_override_options() {
  _impl_._has_bits_[0] &= ~0x00000002u;
  ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* temp = _impl_.override_options_;
  _impl_.override_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* MsaSortOrderOverride::unsafe_arena_release_override_options() {
  // @@protoc_insertion_point(field_release:xla.memory_space_assignment.MsaSortOrderOverride.override_options)
  _impl_._has_bits_[0] &= ~0x00000002u;
  ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* temp = _impl_.override_options_;
  _impl_.override_options_ = nullptr;
  return temp;
}
inline ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* MsaSortOrderOverride::_internal_mutable_override_options() {
  _impl_._has_bits_[0] |= 0x00000002u;
  if (_impl_.override_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::memory_space_assignment::MsaSortOrderOverrideOptions>(GetArenaForAllocation());
    _impl_.override_options_ = p;
  }
  return _impl_.override_options_;
}
inline ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* MsaSortOrderOverride::mutable_override_options() {
  ::xla::memory_space_assignment::MsaSortOrderOverrideOptions* _msg = _internal_mutable_override_options();
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.MsaSortOrderOverride.override_options)
  return _msg;
}
inline void MsaSortOrderOverride::set_allocated_override_options(::xla::memory_space_assignment::MsaSortOrderOverrideOptions* override_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.override_options_;
  }
  if (override_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(override_options);
    if (message_arena != submessage_arena) {
      override_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, override_options, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.override_options_ = override_options;
  // @@protoc_insertion_point(field_set_allocated:xla.memory_space_assignment.MsaSortOrderOverride.override_options)
}

// optional bool apply_to_cross_program_prefetches = 3;
inline bool MsaSortOrderOverride::_internal_has_apply_to_cross_program_prefetches() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool MsaSortOrderOverride::has_apply_to_cross_program_prefetches() const {
  return _internal_has_apply_to_cross_program_prefetches();
}
inline void MsaSortOrderOverride::clear_apply_to_cross_program_prefetches() {
  _impl_.apply_to_cross_program_prefetches_ = false;
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline bool MsaSortOrderOverride::_internal_apply_to_cross_program_prefetches() const {
  return _impl_.apply_to_cross_program_prefetches_;
}
inline bool MsaSortOrderOverride::apply_to_cross_program_prefetches() const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MsaSortOrderOverride.apply_to_cross_program_prefetches)
  return _internal_apply_to_cross_program_prefetches();
}
inline void MsaSortOrderOverride::_internal_set_apply_to_cross_program_prefetches(bool value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.apply_to_cross_program_prefetches_ = value;
}
inline void MsaSortOrderOverride::set_apply_to_cross_program_prefetches(bool value) {
  _internal_set_apply_to_cross_program_prefetches(value);
  // @@protoc_insertion_point(field_set:xla.memory_space_assignment.MsaSortOrderOverride.apply_to_cross_program_prefetches)
}

// -------------------------------------------------------------------

// MsaSortOrderOverrides

// repeated .xla.memory_space_assignment.MsaSortOrderOverride overrides = 1;
inline int MsaSortOrderOverrides::_internal_overrides_size() const {
  return _impl_.overrides_.size();
}
inline int MsaSortOrderOverrides::overrides_size() const {
  return _internal_overrides_size();
}
inline void MsaSortOrderOverrides::clear_overrides() {
  _impl_.overrides_.Clear();
}
inline ::xla::memory_space_assignment::MsaSortOrderOverride* MsaSortOrderOverrides::mutable_overrides(int index) {
  // @@protoc_insertion_point(field_mutable:xla.memory_space_assignment.MsaSortOrderOverrides.overrides)
  return _impl_.overrides_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::MsaSortOrderOverride >*
MsaSortOrderOverrides::mutable_overrides() {
  // @@protoc_insertion_point(field_mutable_list:xla.memory_space_assignment.MsaSortOrderOverrides.overrides)
  return &_impl_.overrides_;
}
inline const ::xla::memory_space_assignment::MsaSortOrderOverride& MsaSortOrderOverrides::_internal_overrides(int index) const {
  return _impl_.overrides_.Get(index);
}
inline const ::xla::memory_space_assignment::MsaSortOrderOverride& MsaSortOrderOverrides::overrides(int index) const {
  // @@protoc_insertion_point(field_get:xla.memory_space_assignment.MsaSortOrderOverrides.overrides)
  return _internal_overrides(index);
}
inline ::xla::memory_space_assignment::MsaSortOrderOverride* MsaSortOrderOverrides::_internal_add_overrides() {
  return _impl_.overrides_.Add();
}
inline ::xla::memory_space_assignment::MsaSortOrderOverride* MsaSortOrderOverrides::add_overrides() {
  ::xla::memory_space_assignment::MsaSortOrderOverride* _add = _internal_add_overrides();
  // @@protoc_insertion_point(field_add:xla.memory_space_assignment.MsaSortOrderOverrides.overrides)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::memory_space_assignment::MsaSortOrderOverride >&
MsaSortOrderOverrides::overrides() const {
  // @@protoc_insertion_point(field_list:xla.memory_space_assignment.MsaSortOrderOverrides.overrides)
  return _impl_.overrides_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace memory_space_assignment
}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fmemory_5fspace_5fassignment_2fmemory_5fspace_5fassignment_2eproto

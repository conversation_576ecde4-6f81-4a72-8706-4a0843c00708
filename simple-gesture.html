<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Gesture Recognition</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .video-container {
            position: relative;
            width: 100%;
            max-width: 640px;
            margin: 0 auto 20px;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: auto;
            display: block;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        button.active {
            background: #27ae60;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .gesture-display {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
            border-left: 4px solid #27ae60;
        }
        .gesture-text {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
        }
        .confidence {
            font-size: 14px;
            color: #666;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #856404;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤲 Simple Hand Gesture Recognition</h1>
        
        <div class="video-container">
            <video id="video" autoplay muted playsinline></video>
        </div>
        
        <div class="controls">
            <button id="startBtn">Start Camera</button>
            <button id="gestureBtn" disabled>Start Gesture Recognition</button>
            <button id="stopBtn" disabled>Stop</button>
        </div>
        
        <div class="status" id="status">
            Click "Start Camera" to begin
        </div>
        
        <div class="gesture-display" id="gestureDisplay" style="display: none;">
            <div class="gesture-text" id="gestureText">No gesture detected</div>
            <div class="confidence" id="confidence">Confidence: 0%</div>
        </div>
        
        <div class="instructions">
            <h3>📋 How to Use:</h3>
            <ul>
                <li><strong>Step 1:</strong> Click "Start Camera" and allow camera access</li>
                <li><strong>Step 2:</strong> Click "Start Gesture Recognition"</li>
                <li><strong>Step 3:</strong> Show your hand to the camera and make gestures</li>
            </ul>
            
            <h3>👋 Supported Gestures:</h3>
            <ul>
                <li>✊ <strong>Closed Fist</strong> → "Stop"</li>
                <li>☝️ <strong>One Finger</strong> → "1"</li>
                <li>✌️ <strong>Two Fingers</strong> → "2"</li>
                <li>🤟 <strong>Three Fingers</strong> → "3"</li>
                <li>🖐️ <strong>Open Hand</strong> → "Hello"</li>
                <li>👍 <strong>Thumbs Up</strong> → "Good"</li>
            </ul>
        </div>
    </div>

    <script>
        // DOM Elements
        const video = document.getElementById('video');
        const startBtn = document.getElementById('startBtn');
        const gestureBtn = document.getElementById('gestureBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const gestureDisplay = document.getElementById('gestureDisplay');
        const gestureText = document.getElementById('gestureText');
        const confidence = document.getElementById('confidence');

        // State
        let isDetecting = false;
        let stream = null;
        let detectionInterval = null;

        // Simple gesture detection based on hand position
        function detectSimpleGestures() {
            // This is a simplified gesture detection
            // In a real implementation, you would use MediaPipe or TensorFlow.js
            
            const gestures = ['Stop', '1', '2', '3', 'Hello', 'Good'];
            const randomGesture = gestures[Math.floor(Math.random() * gestures.length)];
            const randomConfidence = Math.floor(Math.random() * 30) + 70; // 70-100%
            
            gestureText.textContent = randomGesture;
            confidence.textContent = `Confidence: ${randomConfidence}%`;
            
            console.log(`Detected gesture: ${randomGesture} (${randomConfidence}%)`);
        }

        // Start camera
        async function startCamera() {
            try {
                status.textContent = 'Starting camera...';
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { width: 640, height: 480 } 
                });
                video.srcObject = stream;
                
                video.onloadedmetadata = () => {
                    status.textContent = 'Camera started! Click "Start Gesture Recognition"';
                    startBtn.disabled = true;
                    gestureBtn.disabled = false;
                    stopBtn.disabled = false;
                };
            } catch (error) {
                status.textContent = 'Error: Could not access camera. Please allow camera permission.';
                console.error('Camera error:', error);
            }
        }

        // Start gesture recognition
        function startGestureRecognition() {
            if (!stream) {
                status.textContent = 'Please start camera first!';
                return;
            }
            
            isDetecting = true;
            gestureDisplay.style.display = 'block';
            status.textContent = 'Gesture recognition active - Show your hand!';
            gestureBtn.textContent = 'Stop Gesture Recognition';
            gestureBtn.classList.add('active');
            gestureBtn.onclick = stopGestureRecognition;
            
            // Start detection loop
            detectionInterval = setInterval(detectSimpleGestures, 2000); // Every 2 seconds
        }
        
        // Stop gesture recognition
        function stopGestureRecognition() {
            isDetecting = false;
            gestureDisplay.style.display = 'none';
            status.textContent = 'Gesture recognition stopped';
            gestureBtn.textContent = 'Start Gesture Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.onclick = startGestureRecognition;
            
            if (detectionInterval) {
                clearInterval(detectionInterval);
                detectionInterval = null;
            }
        }
        
        // Stop everything
        function stop() {
            isDetecting = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            gestureDisplay.style.display = 'none';
            status.textContent = 'Stopped. Click "Start Camera" to begin again.';
            startBtn.disabled = false;
            gestureBtn.disabled = true;
            gestureBtn.textContent = 'Start Gesture Recognition';
            gestureBtn.classList.remove('active');
            gestureBtn.onclick = startGestureRecognition;
            stopBtn.disabled = true;
            
            if (detectionInterval) {
                clearInterval(detectionInterval);
                detectionInterval = null;
            }
        }

        // Event listeners
        startBtn.addEventListener('click', startCamera);
        gestureBtn.addEventListener('click', startGestureRecognition);
        stopBtn.addEventListener('click', stop);

        // Initialize
        console.log('Simple Gesture Recognition Demo Ready!');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Language Video Call</title>
    <link rel="stylesheet" href="call-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- TensorFlow.js for Hand Gesture Recognition -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/handpose@0.0.7/dist/handpose.min.js"></script>
</head>
<body>
    <div class="call-container">
        <!-- Header -->
        <header class="call-header">
            <div class="call-info">
                <h1><i class="fas fa-hands"></i> Sign Language Call</h1>
                <div class="call-status">
                    <span class="status-dot" id="callStatus"></span>
                    <span id="callStatusText">Ready to Connect</span>
                </div>
            </div>
            <div class="call-timer">
                <i class="fas fa-clock"></i>
                <span id="callTimer">00:00</span>
            </div>
        </header>

        <!-- Main Video Area -->
        <main class="video-area">
            <!-- Remote User (Sign Language Speaker) -->
            <div class="video-panel remote-panel">
                <div class="video-header">
                    <div class="user-info">
                        <i class="fas fa-user"></i>
                        <span>Sign Language User</span>
                    </div>
                    <div class="video-controls">
                        <button class="control-btn" id="muteRemoteBtn" title="Mute Remote Audio">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
                <div class="video-container">
                    <video id="remoteVideo" autoplay playsinline></video>
                    <canvas id="remoteCanvas" class="gesture-canvas"></canvas>
                    <div class="video-placeholder" id="remotePlaceholder">
                        <i class="fas fa-user-circle"></i>
                        <p>Waiting for connection...</p>
                    </div>
                    <div class="gesture-overlay" id="remoteGestureOverlay">
                        <div class="gesture-indicator">
                            <i class="fas fa-hand-paper"></i>
                            <span id="remoteGestureText">Gesture Detected</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Local User (Non-Speaking Interpreter) -->
            <div class="video-panel local-panel">
                <div class="video-header">
                    <div class="user-info">
                        <i class="fas fa-user-check"></i>
                        <span>You (Interpreter)</span>
                    </div>
                    <div class="video-controls">
                        <button class="control-btn" id="toggleVideoBtn" title="Toggle Camera">
                            <i class="fas fa-video"></i>
                        </button>
                        <button class="control-btn" id="toggleAudioBtn" title="Toggle Microphone">
                            <i class="fas fa-microphone-slash"></i>
                        </button>
                        <button class="control-btn" id="toggleGestureBtn" title="Toggle Gesture Recognition">
                            <i class="fas fa-hand-paper"></i>
                        </button>
                    </div>
                </div>
                <div class="video-container">
                    <video id="localVideo" autoplay muted playsinline></video>
                    <canvas id="localCanvas" class="gesture-canvas"></canvas>
                    <div class="video-placeholder" id="localPlaceholder">
                        <i class="fas fa-video-slash"></i>
                        <p>Camera Off</p>
                        <button class="start-camera-btn" id="startCameraBtn">
                            <i class="fas fa-play"></i>
                            Start Camera
                        </button>
                    </div>
                    <div class="gesture-status" id="gestureStatus">
                        <div class="gesture-indicator active">
                            <i class="fas fa-hand-paper"></i>
                            <span id="localGestureText">Ready for gestures</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Communication Panel -->
        <section class="communication-panel">
            <!-- Text Translation Area -->
            <div class="translation-area">
                <div class="translation-header">
                    <h3><i class="fas fa-language"></i> Live Translation</h3>
                    <div class="translation-controls">
                        <button class="control-btn secondary" id="clearTranslationBtn">
                            <i class="fas fa-eraser"></i>
                            Clear
                        </button>
                        <button class="control-btn secondary" id="saveTranslationBtn">
                            <i class="fas fa-save"></i>
                            Save
                        </button>
                    </div>
                </div>
                <div class="translation-display" id="translationDisplay">
                    <div class="translation-placeholder">
                        <i class="fas fa-comments"></i>
                        <p>Sign language translations will appear here</p>
                    </div>
                </div>
            </div>

            <!-- Response Area -->
            <div class="response-area">
                <div class="response-header">
                    <h3><i class="fas fa-reply"></i> Your Response</h3>
                    <div class="response-mode">
                        <label class="mode-switch">
                            <input type="radio" name="responseMode" value="text" checked>
                            <span>Text</span>
                        </label>
                        <label class="mode-switch">
                            <input type="radio" name="responseMode" value="gesture">
                            <span>Gesture</span>
                        </label>
                    </div>
                </div>

                <!-- Text Response -->
                <div class="response-input" id="textResponse">
                    <textarea id="responseText" placeholder="Type your response here..." rows="4"></textarea>
                    <div class="input-controls">
                        <button class="control-btn" id="sendTextBtn">
                            <i class="fas fa-paper-plane"></i>
                            Send Text
                        </button>
                        <button class="control-btn secondary" id="textToSignBtn">
                            <i class="fas fa-hand-point-right"></i>
                            Convert to Sign
                        </button>
                    </div>
                </div>

                <!-- Gesture Response -->
                <div class="response-input hidden" id="gestureResponse">
                    <div class="gesture-area">
                        <div class="gesture-preview">
                            <i class="fas fa-hands"></i>
                            <p>Make gestures in front of camera</p>
                        </div>
                        <div class="gesture-controls">
                            <button class="control-btn" id="recordGestureBtn">
                                <i class="fas fa-record-vinyl"></i>
                                Record Gesture
                            </button>
                            <button class="control-btn secondary" id="sendGestureBtn" disabled>
                                <i class="fas fa-share"></i>
                                Send Gesture
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call Controls -->
        <footer class="call-controls">
            <div class="control-group">
                <button class="control-btn large" id="connectBtn">
                    <i class="fas fa-phone"></i>
                    <span>Connect Call</span>
                </button>
                <button class="control-btn large secondary hidden" id="endCallBtn">
                    <i class="fas fa-phone-slash"></i>
                    <span>End Call</span>
                </button>
            </div>

            <div class="control-group">
                <button class="control-btn" id="fullscreenBtn" title="Fullscreen">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="control-btn" id="settingsBtn" title="Settings">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="control-btn" id="helpBtn" title="Help">
                    <i class="fas fa-question-circle"></i>
                </button>
            </div>
        </footer>

        <!-- Connection Status -->
        <div class="connection-status" id="connectionStatus">
            <div class="status-content">
                <div class="status-icon">
                    <i class="fas fa-wifi"></i>
                </div>
                <div class="status-info">
                    <h4>Connection Quality</h4>
                    <div class="quality-bars">
                        <div class="bar active"></div>
                        <div class="bar active"></div>
                        <div class="bar active"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <span class="quality-text">Good</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div class="notification-toast" id="notificationToast">
        <div class="toast-content">
            <i class="fas fa-info-circle"></i>
            <span id="toastMessage"></span>
        </div>
    </div>

    <script src="call-script.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Hand Gesture Recognition</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .video-container {
            position: relative;
            width: 100%;
            max-width: 640px;
            margin: 0 auto 20px;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: auto;
            display: block;
        }
        canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        button.active {
            background: #27ae60;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .gesture-display {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
            border-left: 4px solid #27ae60;
        }
        .gesture-text {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
        }
        .confidence {
            font-size: 14px;
            color: #666;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #856404;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤲 Simple Hand Gesture Recognition</h1>
        
        <div class="video-container">
            <video id="video" autoplay muted playsinline></video>
            <canvas id="canvas"></canvas>
        </div>
        
        <div class="controls">
            <button id="startBtn">Start Camera</button>
            <button id="gestureBtn" disabled>Start Gesture Recognition</button>
            <button id="stopBtn" disabled>Stop</button>
        </div>
        
        <div class="status" id="status">
            Click "Start Camera" to begin
        </div>
        
        <div class="gesture-display" id="gestureDisplay" style="display: none;">
            <div class="gesture-text" id="gestureText">No gesture detected</div>
            <div class="confidence" id="confidence">Confidence: 0%</div>
        </div>
        
        <div class="instructions">
            <h3>📋 How to Use:</h3>
            <ul>
                <li><strong>Step 1:</strong> Click "Start Camera" and allow camera access</li>
                <li><strong>Step 2:</strong> Click "Start Gesture Recognition" (wait for model to load)</li>
                <li><strong>Step 3:</strong> Show your hand to the camera and make gestures</li>
            </ul>
            
            <h3>👋 Supported Gestures:</h3>
            <ul>
                <li>✊ <strong>Closed Fist</strong> → "Stop"</li>
                <li>☝️ <strong>One Finger</strong> → "1"</li>
                <li>✌️ <strong>Two Fingers</strong> → "2"</li>
                <li>🤟 <strong>Three Fingers</strong> → "3"</li>
                <li>🖐️ <strong>Open Hand</strong> → "Hello"</li>
            </ul>
        </div>
    </div>

    <!-- TensorFlow.js -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/handpose@0.0.7/dist/handpose.min.js"></script>

    <script>
        // DOM Elements
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const startBtn = document.getElementById('startBtn');
        const gestureBtn = document.getElementById('gestureBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const gestureDisplay = document.getElementById('gestureDisplay');
        const gestureText = document.getElementById('gestureText');
        const confidence = document.getElementById('confidence');

        // State
        let model = null;
        let isDetecting = false;
        let stream = null;

        // Start camera
        async function startCamera() {
            try {
                status.textContent = 'Starting camera...';
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { width: 640, height: 480 } 
                });
                video.srcObject = stream;
                
                video.onloadedmetadata = () => {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    status.textContent = 'Camera started! Click "Start Gesture Recognition"';
                    startBtn.disabled = true;
                    gestureBtn.disabled = false;
                    stopBtn.disabled = false;
                };
            } catch (error) {
                status.textContent = 'Error: Could not access camera. Please allow camera permission.';
                console.error('Camera error:', error);
            }
        }

        // Load model and start detection
        async function startGestureRecognition() {
            try {
                status.textContent = 'Loading gesture recognition model...';
                gestureBtn.disabled = true;
                
                // Load handpose model
                model = await handpose.load();
                
                status.textContent = 'Model loaded! Show your hand to the camera';
                gestureDisplay.style.display = 'block';
                gestureBtn.classList.add('active');
                gestureBtn.textContent = 'Gesture Recognition Active';
                
                isDetecting = true;
                detectGestures();
                
            } catch (error) {
                status.textContent = 'Error loading model: ' + error.message;
                console.error('Model loading error:', error);
                gestureBtn.disabled = false;
            }
        }

        // Detect gestures
        async function detectGestures() {
            if (!isDetecting || !model) return;

            try {
                const predictions = await model.estimateHands(video);
                
                // Clear canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                if (predictions.length > 0) {
                    const hand = predictions[0];
                    
                    // Draw hand landmarks
                    drawHand(hand.landmarks);
                    
                    // Classify gesture
                    const gesture = classifyGesture(hand.landmarks);
                    const conf = Math.round((hand.handInViewConfidence || 0.8) * 100);
                    
                    gestureText.textContent = gesture;
                    confidence.textContent = `Confidence: ${conf}%`;
                    
                } else {
                    gestureText.textContent = 'No hand detected';
                    confidence.textContent = 'Show your hand to the camera';
                }
                
            } catch (error) {
                console.error('Detection error:', error);
            }
            
            // Continue detection
            if (isDetecting) {
                requestAnimationFrame(detectGestures);
            }
        }

        // Draw hand landmarks
        function drawHand(landmarks) {
            // Draw connections
            const connections = [
                [0, 1], [1, 2], [2, 3], [3, 4], // Thumb
                [0, 5], [5, 6], [6, 7], [7, 8], // Index
                [0, 9], [9, 10], [10, 11], [11, 12], // Middle
                [0, 13], [13, 14], [14, 15], [15, 16], // Ring
                [0, 17], [17, 18], [18, 19], [19, 20], // Pinky
                [5, 9], [9, 13], [13, 17] // Palm
            ];
            
            // Draw lines
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            for (const [start, end] of connections) {
                const startPoint = landmarks[start];
                const endPoint = landmarks[end];
                ctx.beginPath();
                ctx.moveTo(startPoint[0], startPoint[1]);
                ctx.lineTo(endPoint[0], endPoint[1]);
                ctx.stroke();
            }
            
            // Draw points
            ctx.fillStyle = '#ff0000';
            for (const landmark of landmarks) {
                ctx.beginPath();
                ctx.arc(landmark[0], landmark[1], 4, 0, 2 * Math.PI);
                ctx.fill();
            }
        }

        // Simple gesture classification
        function classifyGesture(landmarks) {
            if (!landmarks || landmarks.length < 21) return 'Unknown';
            
            // Get finger tips and joints
            const thumbTip = landmarks[4];
            const thumbJoint = landmarks[3];
            const indexTip = landmarks[8];
            const indexJoint = landmarks[6];
            const middleTip = landmarks[12];
            const middleJoint = landmarks[10];
            const ringTip = landmarks[16];
            const ringJoint = landmarks[14];
            const pinkyTip = landmarks[20];
            const pinkyJoint = landmarks[18];
            
            // Count extended fingers
            let extended = 0;
            
            // Check each finger
            if (thumbTip[0] > thumbJoint[0]) extended++; // Thumb
            if (indexTip[1] < indexJoint[1]) extended++; // Index
            if (middleTip[1] < middleJoint[1]) extended++; // Middle
            if (ringTip[1] < ringJoint[1]) extended++; // Ring
            if (pinkyTip[1] < pinkyJoint[1]) extended++; // Pinky
            
            // Classify based on extended fingers
            switch (extended) {
                case 0: return 'Stop';
                case 1: return '1';
                case 2: return '2';
                case 3: return '3';
                case 4: return '4';
                case 5: return 'Hello';
                default: return 'Unknown';
            }
        }

        // Stop everything
        function stop() {
            isDetecting = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            video.srcObject = null;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            startBtn.disabled = false;
            gestureBtn.disabled = true;
            gestureBtn.classList.remove('active');
            gestureBtn.textContent = 'Start Gesture Recognition';
            stopBtn.disabled = true;
            
            status.textContent = 'Stopped. Click "Start Camera" to begin again.';
            gestureDisplay.style.display = 'none';
        }

        // Event listeners
        startBtn.addEventListener('click', startCamera);
        gestureBtn.addEventListener('click', startGestureRecognition);
        stopBtn.addEventListener('click', stop);

        // Initialize
        console.log('Simple Gesture Recognition Demo Ready!');
    </script>
</body>
</html>

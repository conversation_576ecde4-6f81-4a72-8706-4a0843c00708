const mongoose = require('mongoose');

const callSchema = new mongoose.Schema({
    callId: {
        type: String,
        unique: true,
        required: true
    },
    room: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Room',
        required: true
    },
    participants: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        role: {
            type: String,
            enum: ['caller', 'interpreter', 'participant'],
            required: true
        },
        joinedAt: {
            type: Date,
            default: Date.now
        },
        leftAt: Date,
        connectionQuality: {
            video: {
                type: String,
                enum: ['poor', 'fair', 'good', 'excellent'],
                default: 'good'
            },
            audio: {
                type: String,
                enum: ['poor', 'fair', 'good', 'excellent'],
                default: 'good'
            },
            latency: Number, // in milliseconds
            packetLoss: Number // percentage
        }
    }],
    status: {
        type: String,
        enum: ['initiated', 'ringing', 'connected', 'ended', 'failed'],
        default: 'initiated'
    },
    startTime: Date,
    endTime: Date,
    duration: {
        type: Number,
        default: 0 // in seconds
    },
    recording: {
        enabled: {
            type: Boolean,
            default: false
        },
        url: String,
        size: Number, // in bytes
        format: {
            type: String,
            enum: ['mp4', 'webm', 'avi'],
            default: 'mp4'
        },
        startedAt: Date,
        endedAt: Date
    },
    transcription: [{
        timestamp: {
            type: Date,
            default: Date.now
        },
        speaker: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        content: {
            type: String,
            required: true
        },
        type: {
            type: String,
            enum: ['speech', 'sign_language', 'text', 'gesture'],
            required: true
        },
        confidence: {
            type: Number,
            min: 0,
            max: 1,
            default: 1
        },
        language: {
            type: String,
            enum: ['ASL', 'BSL', 'ISL', 'JSL', 'CSL', 'FSL', 'GSL', 'Other'],
            default: 'ASL'
        },
        translation: {
            text: String,
            confidence: Number
        }
    }],
    messages: [{
        sender: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        content: {
            type: String,
            required: true
        },
        type: {
            type: String,
            enum: ['text', 'system', 'gesture_description'],
            default: 'text'
        },
        timestamp: {
            type: Date,
            default: Date.now
        },
        readBy: [{
            user: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            readAt: {
                type: Date,
                default: Date.now
            }
        }]
    }],
    qualityMetrics: {
        averageLatency: Number,
        averagePacketLoss: Number,
        videoQuality: {
            type: String,
            enum: ['poor', 'fair', 'good', 'excellent']
        },
        audioQuality: {
            type: String,
            enum: ['poor', 'fair', 'good', 'excellent']
        },
        connectionStability: {
            type: String,
            enum: ['unstable', 'stable', 'very_stable']
        },
        userSatisfaction: [{
            user: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            rating: {
                type: Number,
                min: 1,
                max: 5
            },
            feedback: String,
            submittedAt: {
                type: Date,
                default: Date.now
            }
        }]
    },
    technicalData: {
        webrtcStats: mongoose.Schema.Types.Mixed,
        errors: [{
            timestamp: {
                type: Date,
                default: Date.now
            },
            type: String,
            message: String,
            stack: String,
            user: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            }
        }],
        bandwidth: {
            upload: Number, // kbps
            download: Number // kbps
        }
    },
    billing: {
        cost: {
            type: Number,
            default: 0
        },
        currency: {
            type: String,
            default: 'USD'
        },
        billingRate: Number, // per minute
        interpreterFee: Number
    }
}, {
    timestamps: true
});

// Indexes for better performance
callSchema.index({ callId: 1 });
callSchema.index({ room: 1 });
callSchema.index({ 'participants.user': 1 });
callSchema.index({ status: 1 });
callSchema.index({ startTime: 1 });
callSchema.index({ createdAt: -1 });

// Virtual for call duration in minutes
callSchema.virtual('durationInMinutes').get(function() {
    return Math.ceil(this.duration / 60);
});

// Virtual for active participants
callSchema.virtual('activeParticipants').get(function() {
    return this.participants.filter(p => !p.leftAt);
});

// Method to start call
callSchema.methods.startCall = function() {
    this.status = 'connected';
    this.startTime = new Date();
    return this.save();
};

// Method to end call
callSchema.methods.endCall = function() {
    this.status = 'ended';
    this.endTime = new Date();
    
    if (this.startTime) {
        this.duration = Math.floor((this.endTime - this.startTime) / 1000);
    }
    
    // Mark all participants as left
    this.participants.forEach(participant => {
        if (!participant.leftAt) {
            participant.leftAt = new Date();
        }
    });
    
    return this.save();
};

// Method to add participant
callSchema.methods.addParticipant = function(userId, role = 'participant') {
    const existingParticipant = this.participants.find(
        p => p.user.toString() === userId.toString() && !p.leftAt
    );
    
    if (existingParticipant) {
        throw new Error('User is already in the call');
    }
    
    this.participants.push({
        user: userId,
        role: role,
        joinedAt: new Date()
    });
    
    return this.save();
};

// Method to remove participant
callSchema.methods.removeParticipant = function(userId) {
    const participant = this.participants.find(
        p => p.user.toString() === userId.toString() && !p.leftAt
    );
    
    if (participant) {
        participant.leftAt = new Date();
    }
    
    return this.save();
};

// Method to add transcription
callSchema.methods.addTranscription = function(speakerId, content, type, confidence = 1, language = 'ASL') {
    this.transcription.push({
        timestamp: new Date(),
        speaker: speakerId,
        content: content,
        type: type,
        confidence: confidence,
        language: language
    });
    
    return this.save();
};

// Method to add message
callSchema.methods.addMessage = function(senderId, content, type = 'text') {
    this.messages.push({
        sender: senderId,
        content: content,
        type: type,
        timestamp: new Date()
    });
    
    return this.save();
};

// Method to update quality metrics
callSchema.methods.updateQualityMetrics = function(userId, metrics) {
    const participant = this.participants.find(
        p => p.user.toString() === userId.toString()
    );
    
    if (participant) {
        participant.connectionQuality = {
            ...participant.connectionQuality,
            ...metrics
        };
    }
    
    return this.save();
};

// Method to add error log
callSchema.methods.logError = function(userId, errorType, message, stack = null) {
    this.technicalData.errors.push({
        timestamp: new Date(),
        type: errorType,
        message: message,
        stack: stack,
        user: userId
    });
    
    return this.save();
};

// Method to calculate cost
callSchema.methods.calculateCost = function() {
    if (this.billing.billingRate && this.duration) {
        const minutes = Math.ceil(this.duration / 60);
        this.billing.cost = minutes * this.billing.billingRate;
        
        if (this.billing.interpreterFee) {
            this.billing.cost += this.billing.interpreterFee;
        }
    }
    
    return this.billing.cost;
};

// Static method to get call statistics
callSchema.statics.getCallStatistics = function(userId, startDate, endDate) {
    const matchStage = {
        'participants.user': mongoose.Types.ObjectId(userId),
        createdAt: {
            $gte: startDate,
            $lte: endDate
        }
    };
    
    return this.aggregate([
        { $match: matchStage },
        {
            $group: {
                _id: null,
                totalCalls: { $sum: 1 },
                totalDuration: { $sum: '$duration' },
                averageDuration: { $avg: '$duration' },
                successfulCalls: {
                    $sum: {
                        $cond: [{ $eq: ['$status', 'ended'] }, 1, 0]
                    }
                },
                totalCost: { $sum: '$billing.cost' }
            }
        }
    ]);
};

module.exports = mongoose.model('Call', callSchema);

// DOM Elements
const connectBtn = document.getElementById('connectBtn');
const endCallBtn = document.getElementById('endCallBtn');
const startCameraBtn = document.getElementById('startCameraBtn');
const toggleVideoBtn = document.getElementById('toggleVideoBtn');
const toggleAudioBtn = document.getElementById('toggleAudioBtn');
const toggleGestureBtn = document.getElementById('toggleGestureBtn');
const muteRemoteBtn = document.getElementById('muteRemoteBtn');
const localVideo = document.getElementById('localVideo');
const remoteVideo = document.getElementById('remoteVideo');
const localPlaceholder = document.getElementById('localPlaceholder');
const remotePlaceholder = document.getElementById('remotePlaceholder');
const callStatus = document.getElementById('callStatus');
const callStatusText = document.getElementById('callStatusText');
const callTimer = document.getElementById('callTimer');
const translationDisplay = document.getElementById('translationDisplay');
const responseText = document.getElementById('responseText');
const sendTextBtn = document.getElementById('sendTextBtn');
const textToSignBtn = document.getElementById('textToSignBtn');
const recordGestureBtn = document.getElementById('recordGestureBtn');
const sendGestureBtn = document.getElementById('sendGestureBtn');
const clearTranslationBtn = document.getElementById('clearTranslationBtn');
const saveTranslationBtn = document.getElementById('saveTranslationBtn');
const remoteGestureOverlay = document.getElementById('remoteGestureOverlay');
const localCanvas = document.getElementById('localCanvas');
const remoteCanvas = document.getElementById('remoteCanvas');
const gestureStatus = document.getElementById('gestureStatus');
const localGestureText = document.getElementById('localGestureText');
const remoteGestureText = document.getElementById('remoteGestureText');
const notificationToast = document.getElementById('notificationToast');
const toastMessage = document.getElementById('toastMessage');

// Application State
let isConnected = false;
let isVideoOn = false;
let isAudioOn = false;
let isRemoteAudioOn = true;
let localStream = null;
let callStartTime = null;
let callTimerInterval = null;
let gestureRecording = false;

// Gesture Recognition State
let isGestureRecognitionOn = false;
let handposeModel = null;
let gestureDetectionInterval = null;
let lastGestureTime = 0;
let currentGesture = null;
let gestureConfidence = 0;

// Sign Language Gesture Dictionary
const gestureLibrary = {
    // Basic greetings
    'open_palm': 'Hello',
    'wave': 'Hi',
    'thumbs_up': 'Good',
    'thumbs_down': 'Bad',
    'peace_sign': 'Peace',
    'ok_sign': 'OK',
    'pointing_up': 'Yes',
    'pointing_down': 'No',
    'fist': 'Stop',
    'open_hand': 'Wait',

    // Common phrases
    'prayer_hands': 'Please',
    'clapping': 'Thank you',
    'heart_hands': 'Love',
    'shrug': 'I don\'t know',
    'finger_to_lips': 'Quiet',
    'hand_to_ear': 'Listen',
    'hand_to_mouth': 'Eat',
    'drinking_gesture': 'Drink',
    'sleeping_gesture': 'Sleep',
    'money_gesture': 'Money',

    // Emotions
    'happy_gesture': 'Happy',
    'sad_gesture': 'Sad',
    'angry_gesture': 'Angry',
    'surprised_gesture': 'Surprised',
    'confused_gesture': 'Confused',

    // Numbers (basic)
    'one_finger': '1',
    'two_fingers': '2',
    'three_fingers': '3',
    'four_fingers': '4',
    'five_fingers': '5'
};

// Simulated sign language translations for demo
const signLanguageTranslations = [
    "Hello, how are you today?",
    "I'm fine, thank you for asking",
    "Can you help me with something?",
    "Yes, I'd be happy to help",
    "What time is the meeting?",
    "The meeting is at 3 PM",
    "Thank you very much",
    "You're welcome",
    "I understand what you're saying",
    "Please repeat that",
    "I need to go now",
    "Have a great day!",
    "See you later",
    "Take care"
];

// Response mode handling
const responseModeInputs = document.querySelectorAll('input[name="responseMode"]');
const textResponse = document.getElementById('textResponse');
const gestureResponse = document.getElementById('gestureResponse');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    updateCallStatus('Ready to Connect', 'ready');
});

function initializeEventListeners() {
    // Call controls
    connectBtn.addEventListener('click', handleConnect);
    endCallBtn.addEventListener('click', handleEndCall);

    // Camera controls
    startCameraBtn.addEventListener('click', startLocalCamera);
    toggleVideoBtn.addEventListener('click', toggleVideo);
    toggleAudioBtn.addEventListener('click', toggleAudio);
    toggleGestureBtn.addEventListener('click', toggleGestureRecognition);
    muteRemoteBtn.addEventListener('click', toggleRemoteAudio);

    // Communication controls
    sendTextBtn.addEventListener('click', sendTextMessage);
    textToSignBtn.addEventListener('click', convertTextToSign);
    recordGestureBtn.addEventListener('click', toggleGestureRecording);
    sendGestureBtn.addEventListener('click', sendGestureMessage);
    clearTranslationBtn.addEventListener('click', clearTranslations);
    saveTranslationBtn.addEventListener('click', saveTranslations);

    // Response mode switching
    responseModeInputs.forEach(input => {
        input.addEventListener('change', switchResponseMode);
    });

    // Enter key for text input
    responseText.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendTextMessage();
        }
    });
}

async function handleConnect() {
    try {
        updateCallStatus('Connecting...', 'connecting');
        showNotification('Connecting to sign language user...');

        // Simulate connection delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Start the call
        isConnected = true;
        callStartTime = Date.now();
        startCallTimer();

        // Update UI
        connectBtn.classList.add('hidden');
        endCallBtn.classList.remove('hidden');
        updateCallStatus('Connected', 'connected');

        // Show remote video (simulated)
        simulateRemoteVideo();

        // Start simulated sign language recognition
        startSignLanguageSimulation();

        showNotification('Connected! You can now communicate via sign language.');

    } catch (error) {
        console.error('Connection failed:', error);
        updateCallStatus('Connection Failed', 'disconnected');
        showNotification('Failed to connect. Please try again.');
    }
}

function handleEndCall() {
    isConnected = false;
    stopCallTimer();

    // Update UI
    endCallBtn.classList.add('hidden');
    connectBtn.classList.remove('hidden');
    updateCallStatus('Call Ended', 'disconnected');

    // Hide remote video
    remoteVideo.style.display = 'none';
    remotePlaceholder.style.display = 'flex';
    remotePlaceholder.innerHTML = `
        <i class="fas fa-user-circle"></i>
        <p>Call ended</p>
    `;

    showNotification('Call ended successfully.');
}

async function startLocalCamera() {
    try {
        localStream = await navigator.mediaDevices.getUserMedia({
            video: true,
            audio: true
        });

        localVideo.srcObject = localStream;
        localVideo.style.display = 'block';
        localPlaceholder.style.display = 'none';

        isVideoOn = true;
        isAudioOn = true;
        updateVideoButton();
        updateAudioButton();

        showNotification('Camera started successfully.');

    } catch (error) {
        console.error('Failed to start camera:', error);
        showNotification('Failed to access camera. Please check permissions.');
    }
}

function toggleVideo() {
    if (localStream) {
        const videoTrack = localStream.getVideoTracks()[0];
        if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            isVideoOn = videoTrack.enabled;
            updateVideoButton();

            if (isVideoOn) {
                localVideo.style.display = 'block';
                localPlaceholder.style.display = 'none';
            } else {
                localVideo.style.display = 'none';
                localPlaceholder.style.display = 'flex';
                localPlaceholder.innerHTML = `
                    <i class="fas fa-video-slash"></i>
                    <p>Camera Off</p>
                `;
            }
        }
    }
}

function toggleAudio() {
    if (localStream) {
        const audioTrack = localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            isAudioOn = audioTrack.enabled;
            updateAudioButton();
        }
    }
}

function toggleRemoteAudio() {
    isRemoteAudioOn = !isRemoteAudioOn;
    updateRemoteAudioButton();

    const message = isRemoteAudioOn ? 'Remote audio unmuted' : 'Remote audio muted';
    showNotification(message);
}

function updateVideoButton() {
    const icon = toggleVideoBtn.querySelector('i');
    if (isVideoOn) {
        icon.className = 'fas fa-video';
        toggleVideoBtn.style.background = '#667eea';
    } else {
        icon.className = 'fas fa-video-slash';
        toggleVideoBtn.style.background = '#dc3545';
    }
}

function updateAudioButton() {
    const icon = toggleAudioBtn.querySelector('i');
    if (isAudioOn) {
        icon.className = 'fas fa-microphone';
        toggleAudioBtn.style.background = '#667eea';
    } else {
        icon.className = 'fas fa-microphone-slash';
        toggleAudioBtn.style.background = '#dc3545';
    }
}

function updateRemoteAudioButton() {
    const icon = muteRemoteBtn.querySelector('i');
    if (isRemoteAudioOn) {
        icon.className = 'fas fa-volume-up';
        muteRemoteBtn.style.background = '#667eea';
    } else {
        icon.className = 'fas fa-volume-mute';
        muteRemoteBtn.style.background = '#dc3545';
    }
}

function updateCallStatus(text, status) {
    callStatusText.textContent = text;
    callStatus.className = `status-dot ${status}`;
}

function startCallTimer() {
    callTimerInterval = setInterval(() => {
        if (callStartTime) {
            const elapsed = Date.now() - callStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            callTimer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }, 1000);
}

function stopCallTimer() {
    if (callTimerInterval) {
        clearInterval(callTimerInterval);
        callTimerInterval = null;
    }
    callTimer.textContent = '00:00';
}

function simulateRemoteVideo() {
    // Show remote video placeholder with connected state
    remotePlaceholder.innerHTML = `
        <i class="fas fa-user-check"></i>
        <p>Sign Language User Connected</p>
    `;

    // Simulate video feed after a moment
    setTimeout(() => {
        remoteVideo.style.display = 'block';
        remotePlaceholder.style.display = 'none';

        // Create a colored background to simulate video
        remoteVideo.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
    }, 1000);
}

function startSignLanguageSimulation() {
    // Simulate receiving sign language translations every 5-10 seconds
    const simulateTranslation = () => {
        if (isConnected) {
            const translation = signLanguageTranslations[Math.floor(Math.random() * signLanguageTranslations.length)];
            addTranslation(translation);
            showGestureDetection();

            // Schedule next translation
            setTimeout(simulateTranslation, 5000 + Math.random() * 5000);
        }
    };

    // Start first translation after 3 seconds
    setTimeout(simulateTranslation, 3000);
}

function addTranslation(text) {
    // Remove placeholder if it exists
    const placeholder = translationDisplay.querySelector('.translation-placeholder');
    if (placeholder) {
        placeholder.remove();
    }

    // Create translation message
    const message = document.createElement('div');
    message.className = 'translation-message';

    const timestamp = new Date().toLocaleTimeString();
    message.innerHTML = `
        <div class="timestamp">${timestamp}</div>
        <div class="text">${text}</div>
    `;

    translationDisplay.appendChild(message);
    translationDisplay.scrollTop = translationDisplay.scrollHeight;
}

function showGestureDetection() {
    remoteGestureOverlay.classList.add('show');
    setTimeout(() => {
        remoteGestureOverlay.classList.remove('show');
    }, 2000);
}

function sendTextMessage() {
    const text = responseText.value.trim();
    if (text && isConnected) {
        // Simulate sending text message
        showNotification(`Text sent: "${text}"`);
        responseText.value = '';

        // Add to translation display as your response
        const message = document.createElement('div');
        message.className = 'translation-message';
        message.style.borderLeftColor = '#28a745';

        const timestamp = new Date().toLocaleTimeString();
        message.innerHTML = `
            <div class="timestamp">You - ${timestamp}</div>
            <div class="text">${text}</div>
        `;

        translationDisplay.appendChild(message);
        translationDisplay.scrollTop = translationDisplay.scrollHeight;
    }
}

function convertTextToSign() {
    const text = responseText.value.trim();
    if (text) {
        showNotification(`Converting "${text}" to sign language...`);
        // Simulate conversion process
        setTimeout(() => {
            showNotification('Text converted to sign language successfully!');
        }, 2000);
    }
}

function toggleGestureRecording() {
    gestureRecording = !gestureRecording;

    if (gestureRecording) {
        recordGestureBtn.innerHTML = '<i class="fas fa-stop"></i> Stop Recording';
        recordGestureBtn.style.background = '#dc3545';
        sendGestureBtn.disabled = true;
        showNotification('Recording gesture... Make your sign now.');

        // Simulate recording for 3 seconds
        setTimeout(() => {
            gestureRecording = false;
            recordGestureBtn.innerHTML = '<i class="fas fa-record-vinyl"></i> Record Gesture';
            recordGestureBtn.style.background = '#667eea';
            sendGestureBtn.disabled = false;
            showNotification('Gesture recorded successfully!');
        }, 3000);
    }
}

function sendGestureMessage() {
    if (isConnected) {
        showNotification('Gesture sent successfully!');
        sendGestureBtn.disabled = true;

        // Re-enable after a moment
        setTimeout(() => {
            sendGestureBtn.disabled = false;
        }, 2000);
    }
}

function switchResponseMode() {
    const selectedMode = document.querySelector('input[name="responseMode"]:checked').value;

    if (selectedMode === 'text') {
        textResponse.classList.remove('hidden');
        gestureResponse.classList.add('hidden');
    } else {
        textResponse.classList.add('hidden');
        gestureResponse.classList.remove('hidden');
    }
}

function clearTranslations() {
    translationDisplay.innerHTML = `
        <div class="translation-placeholder">
            <i class="fas fa-comments"></i>
            <p>Sign language translations will appear here</p>
        </div>
    `;
    showNotification('Translation history cleared.');
}

function saveTranslations() {
    const messages = translationDisplay.querySelectorAll('.translation-message');
    if (messages.length === 0) {
        showNotification('No translations to save.');
        return;
    }

    let content = 'Sign Language Call Transcript\n';
    content += '================================\n\n';

    messages.forEach(message => {
        const timestamp = message.querySelector('.timestamp').textContent;
        const text = message.querySelector('.text').textContent;
        content += `${timestamp}\n${text}\n\n`;
    });

    // Create and download file
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sign-language-call-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    URL.revokeObjectURL(url);

    showNotification('Translation history saved successfully!');
}

function showNotification(message) {
    toastMessage.textContent = message;
    notificationToast.classList.add('show');

    setTimeout(() => {
        notificationToast.classList.remove('show');
    }, 3000);
}

// ===== GESTURE RECOGNITION FUNCTIONS =====

// Initialize TensorFlow.js Handpose
async function initializeGestureRecognition() {
    try {
        showNotification('Loading gesture recognition model...');
        updateGestureStatus('Loading model...', 'loading');

        // Load the handpose model
        handposeModel = await handpose.load();

        showNotification('Gesture recognition model loaded successfully!');
        updateGestureStatus('Model loaded', 'ready');

        return true;
    } catch (error) {
        console.error('Failed to initialize gesture recognition:', error);
        showNotification('Failed to load gesture recognition model');
        updateGestureStatus('Failed to load model', 'error');
        return false;
    }
}

// Start gesture detection loop
function startGestureDetection() {
    if (!handposeModel || !localVideo) {
        console.error('Model or video not ready');
        return;
    }

    gestureDetectionInterval = setInterval(async () => {
        try {
            if (isGestureRecognitionOn && localVideo.readyState === 4) {
                await detectGestures();
            }
        } catch (error) {
            console.error('Gesture detection error:', error);
        }
    }, 200); // Check every 200ms for better performance
}

// Stop gesture detection loop
function stopGestureDetection() {
    if (gestureDetectionInterval) {
        clearInterval(gestureDetectionInterval);
        gestureDetectionInterval = null;
    }
}

// Detect gestures from video
async function detectGestures() {
    try {
        const predictions = await handposeModel.estimateHands(localVideo);

        // Clear canvas
        const canvasCtx = localCanvas.getContext('2d');
        canvasCtx.clearRect(0, 0, localCanvas.width, localCanvas.height);

        // Set canvas size to match video
        localCanvas.width = localVideo.videoWidth || 640;
        localCanvas.height = localVideo.videoHeight || 480;

        if (predictions.length > 0) {
            // Draw hand landmarks
            drawHandLandmarks(canvasCtx, predictions[0].landmarks);

            // Classify gesture
            const gesture = classifyGesture(predictions[0].landmarks);
            const confidence = predictions[0].handInViewConfidence || 0.8;

            if (gesture && confidence > 0.6) {
                const now = Date.now();
                // Only update if gesture is different or enough time has passed
                if (gesture !== currentGesture || now - lastGestureTime > 2000) {
                    currentGesture = gesture;
                    gestureConfidence = confidence;
                    lastGestureTime = now;

                    displayGestureText(gesture, confidence);

                    // Send gesture to other participants
                    if (isConnected) {
                        broadcastGesture(gesture, confidence);
                    }
                }
            }

            // Update status
            updateGestureStatus(`Hand detected (${Math.round(confidence * 100)}%)`, 'detecting');
        } else {
            currentGesture = null;
            updateGestureStatus('Show your hand to camera', 'ready');
        }
    } catch (error) {
        console.error('Error in gesture detection:', error);
        updateGestureStatus('Detection error', 'error');
    }
}

// Draw hand landmarks on canvas
function drawHandLandmarks(ctx, landmarks) {
    // Draw connections
    const connections = [
        [0, 1], [1, 2], [2, 3], [3, 4], // Thumb
        [0, 5], [5, 6], [6, 7], [7, 8], // Index finger
        [0, 9], [9, 10], [10, 11], [11, 12], // Middle finger
        [0, 13], [13, 14], [14, 15], [15, 16], // Ring finger
        [0, 17], [17, 18], [18, 19], [19, 20], // Pinky
        [5, 9], [9, 13], [13, 17] // Palm
    ];

    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 2;

    for (const [start, end] of connections) {
        const startPoint = landmarks[start];
        const endPoint = landmarks[end];

        ctx.beginPath();
        ctx.moveTo(startPoint[0], startPoint[1]);
        ctx.lineTo(endPoint[0], endPoint[1]);
        ctx.stroke();
    }

    // Draw landmarks
    ctx.fillStyle = '#ff0000';
    for (const landmark of landmarks) {
        ctx.beginPath();
        ctx.arc(landmark[0], landmark[1], 4, 0, 2 * Math.PI);
        ctx.fill();
    }
}

// Simple gesture classification based on hand landmarks
function classifyGesture(landmarks) {
    if (!landmarks || landmarks.length < 21) return null;

    // Get key points (TensorFlow.js format: [x, y, z])
    const thumb_tip = landmarks[4];
    const thumb_ip = landmarks[3];
    const index_tip = landmarks[8];
    const index_pip = landmarks[6];
    const middle_tip = landmarks[12];
    const middle_pip = landmarks[10];
    const ring_tip = landmarks[16];
    const ring_pip = landmarks[14];
    const pinky_tip = landmarks[20];
    const pinky_pip = landmarks[18];
    const wrist = landmarks[0];

    // Count extended fingers
    let extendedFingers = 0;

    // Thumb (check if tip is to the right of IP joint)
    if (thumb_tip[0] > thumb_ip[0]) extendedFingers++;

    // Other fingers (check if tip is above PIP joint)
    if (index_tip[1] < index_pip[1]) extendedFingers++;
    if (middle_tip[1] < middle_pip[1]) extendedFingers++;
    if (ring_tip[1] < ring_pip[1]) extendedFingers++;
    if (pinky_tip[1] < pinky_pip[1]) extendedFingers++;

    // Classify based on extended fingers and positions
    if (extendedFingers === 0) {
        return 'Stop';
    } else if (extendedFingers === 1) {
        if (index_tip[1] < index_pip[1]) {
            return '1';
        } else if (thumb_tip[0] > thumb_ip[0]) {
            return 'Good';
        }
    } else if (extendedFingers === 2) {
        if (index_tip[1] < index_pip[1] && middle_tip[1] < middle_pip[1]) {
            return '2';
        }
    } else if (extendedFingers === 3) {
        return '3';
    } else if (extendedFingers === 4) {
        return '4';
    } else if (extendedFingers === 5) {
        return 'Hello';
    }

    // Check for thumbs up (thumb up, other fingers down)
    if (thumb_tip[1] < wrist[1] &&
        index_tip[1] > index_pip[1] &&
        middle_tip[1] > middle_pip[1]) {
        return 'Good';
    }

    // Default to open hand
    return 'Hello';
}

// Display gesture text
function displayGestureText(gesture, confidence = 1.0) {
    localGestureText.textContent = `${gesture} (${Math.round(confidence * 100)}%)`;

    // Show large gesture text overlay
    showGestureOverlay(gesture);

    // Add to translation display
    addTranslation(`You: ${gesture}`, 'gesture');

    console.log(`Gesture detected: ${gesture} with confidence ${Math.round(confidence * 100)}%`);
}

// Show gesture overlay
function showGestureOverlay(gesture) {
    // Remove existing overlay
    const existingOverlay = document.querySelector('.gesture-text-large');
    if (existingOverlay) {
        existingOverlay.remove();
    }

    // Create new overlay
    const overlay = document.createElement('div');
    overlay.className = 'gesture-text-large';
    overlay.textContent = gesture;

    localVideo.parentElement.appendChild(overlay);

    // Remove after 2 seconds
    setTimeout(() => {
        overlay.remove();
    }, 2000);
}

// Update gesture status
function updateGestureStatus(text, status) {
    localGestureText.textContent = text;
    gestureStatus.className = `gesture-status show ${status}`;
}

// Toggle gesture recognition
async function toggleGestureRecognition() {
    if (!isGestureRecognitionOn) {
        if (!localStream) {
            showNotification('Please start camera first');
            return;
        }

        const initialized = await initializeGestureRecognition();
        if (initialized) {
            isGestureRecognitionOn = true;
            toggleGestureBtn.classList.add('active');
            gestureStatus.classList.add('show');

            // Start detection loop
            startGestureDetection();

            showNotification('Gesture recognition started - Show your hand!');
            updateGestureStatus('Ready for gestures', 'ready');
        }
    } else {
        isGestureRecognitionOn = false;
        toggleGestureBtn.classList.remove('active');
        gestureStatus.classList.remove('show');

        // Stop detection loop
        stopGestureDetection();

        // Clear canvas
        const canvasCtx = localCanvas.getContext('2d');
        canvasCtx.clearRect(0, 0, localCanvas.width, localCanvas.height);

        showNotification('Gesture recognition stopped');
    }
}

// Broadcast gesture to other participants
function broadcastGesture(gesture, confidence) {
    // Here you would send the gesture to other participants via WebSocket
    // For now, we'll just log it
    console.log('Broadcasting gesture:', gesture, 'with confidence:', confidence);

    // Simulate receiving the gesture (for demo purposes)
    setTimeout(() => {
        showRemoteGesture(gesture, confidence);
    }, 100);
}

// Show gesture from remote participant
function showRemoteGesture(gesture, confidence) {
    remoteGestureText.textContent = `${gesture} (${Math.round(confidence * 100)}%)`;
    remoteGestureOverlay.classList.add('show');

    setTimeout(() => {
        remoteGestureOverlay.classList.remove('show');
    }, 2000);
}

// Add translation to display
function addTranslation(text, type = 'text') {
    // Remove placeholder if it exists
    const placeholder = translationDisplay.querySelector('.translation-placeholder');
    if (placeholder) {
        placeholder.remove();
    }

    // Create translation message
    const message = document.createElement('div');
    message.className = 'translation-message';
    if (type === 'gesture') {
        message.style.borderLeftColor = '#27ae60';
    }

    const timestamp = new Date().toLocaleTimeString();
    message.innerHTML = `
        <div class="timestamp">${timestamp}</div>
        <div class="text">${text}</div>
    `;

    translationDisplay.appendChild(message);
    translationDisplay.scrollTop = translationDisplay.scrollHeight;
}

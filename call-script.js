// DOM Elements
const connectBtn = document.getElementById('connectBtn');
const endCallBtn = document.getElementById('endCallBtn');
const startCameraBtn = document.getElementById('startCameraBtn');
const toggleVideoBtn = document.getElementById('toggleVideoBtn');
const toggleAudioBtn = document.getElementById('toggleAudioBtn');
const muteRemoteBtn = document.getElementById('muteRemoteBtn');
const localVideo = document.getElementById('localVideo');
const remoteVideo = document.getElementById('remoteVideo');
const localPlaceholder = document.getElementById('localPlaceholder');
const remotePlaceholder = document.getElementById('remotePlaceholder');
const callStatus = document.getElementById('callStatus');
const callStatusText = document.getElementById('callStatusText');
const callTimer = document.getElementById('callTimer');
const translationDisplay = document.getElementById('translationDisplay');
const responseText = document.getElementById('responseText');
const sendTextBtn = document.getElementById('sendTextBtn');
const textToSignBtn = document.getElementById('textToSignBtn');
const recordGestureBtn = document.getElementById('recordGestureBtn');
const sendGestureBtn = document.getElementById('sendGestureBtn');
const clearTranslationBtn = document.getElementById('clearTranslationBtn');
const saveTranslationBtn = document.getElementById('saveTranslationBtn');
const remoteGestureOverlay = document.getElementById('remoteGestureOverlay');
const notificationToast = document.getElementById('notificationToast');
const toastMessage = document.getElementById('toastMessage');

// Application State
let isConnected = false;
let isVideoOn = false;
let isAudioOn = false;
let isRemoteAudioOn = true;
let localStream = null;
let callStartTime = null;
let callTimerInterval = null;
let gestureRecording = false;

// Simulated sign language translations
const signLanguageTranslations = [
    "Hello, how are you today?",
    "I'm fine, thank you for asking",
    "Can you help me with something?",
    "Yes, I'd be happy to help",
    "What time is the meeting?",
    "The meeting is at 3 PM",
    "Thank you very much",
    "You're welcome",
    "I understand what you're saying",
    "Please repeat that",
    "I need to go now",
    "Have a great day!",
    "See you later",
    "Take care"
];

// Response mode handling
const responseModeInputs = document.querySelectorAll('input[name="responseMode"]');
const textResponse = document.getElementById('textResponse');
const gestureResponse = document.getElementById('gestureResponse');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    updateCallStatus('Ready to Connect', 'ready');
});

function initializeEventListeners() {
    // Call controls
    connectBtn.addEventListener('click', handleConnect);
    endCallBtn.addEventListener('click', handleEndCall);
    
    // Camera controls
    startCameraBtn.addEventListener('click', startLocalCamera);
    toggleVideoBtn.addEventListener('click', toggleVideo);
    toggleAudioBtn.addEventListener('click', toggleAudio);
    muteRemoteBtn.addEventListener('click', toggleRemoteAudio);
    
    // Communication controls
    sendTextBtn.addEventListener('click', sendTextMessage);
    textToSignBtn.addEventListener('click', convertTextToSign);
    recordGestureBtn.addEventListener('click', toggleGestureRecording);
    sendGestureBtn.addEventListener('click', sendGestureMessage);
    clearTranslationBtn.addEventListener('click', clearTranslations);
    saveTranslationBtn.addEventListener('click', saveTranslations);
    
    // Response mode switching
    responseModeInputs.forEach(input => {
        input.addEventListener('change', switchResponseMode);
    });
    
    // Enter key for text input
    responseText.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendTextMessage();
        }
    });
}

async function handleConnect() {
    try {
        updateCallStatus('Connecting...', 'connecting');
        showNotification('Connecting to sign language user...');
        
        // Simulate connection delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Start the call
        isConnected = true;
        callStartTime = Date.now();
        startCallTimer();
        
        // Update UI
        connectBtn.classList.add('hidden');
        endCallBtn.classList.remove('hidden');
        updateCallStatus('Connected', 'connected');
        
        // Show remote video (simulated)
        simulateRemoteVideo();
        
        // Start simulated sign language recognition
        startSignLanguageSimulation();
        
        showNotification('Connected! You can now communicate via sign language.');
        
    } catch (error) {
        console.error('Connection failed:', error);
        updateCallStatus('Connection Failed', 'disconnected');
        showNotification('Failed to connect. Please try again.');
    }
}

function handleEndCall() {
    isConnected = false;
    stopCallTimer();
    
    // Update UI
    endCallBtn.classList.add('hidden');
    connectBtn.classList.remove('hidden');
    updateCallStatus('Call Ended', 'disconnected');
    
    // Hide remote video
    remoteVideo.style.display = 'none';
    remotePlaceholder.style.display = 'flex';
    remotePlaceholder.innerHTML = `
        <i class="fas fa-user-circle"></i>
        <p>Call ended</p>
    `;
    
    showNotification('Call ended successfully.');
}

async function startLocalCamera() {
    try {
        localStream = await navigator.mediaDevices.getUserMedia({
            video: true,
            audio: true
        });
        
        localVideo.srcObject = localStream;
        localVideo.style.display = 'block';
        localPlaceholder.style.display = 'none';
        
        isVideoOn = true;
        isAudioOn = true;
        updateVideoButton();
        updateAudioButton();
        
        showNotification('Camera started successfully.');
        
    } catch (error) {
        console.error('Failed to start camera:', error);
        showNotification('Failed to access camera. Please check permissions.');
    }
}

function toggleVideo() {
    if (localStream) {
        const videoTrack = localStream.getVideoTracks()[0];
        if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            isVideoOn = videoTrack.enabled;
            updateVideoButton();
            
            if (isVideoOn) {
                localVideo.style.display = 'block';
                localPlaceholder.style.display = 'none';
            } else {
                localVideo.style.display = 'none';
                localPlaceholder.style.display = 'flex';
                localPlaceholder.innerHTML = `
                    <i class="fas fa-video-slash"></i>
                    <p>Camera Off</p>
                `;
            }
        }
    }
}

function toggleAudio() {
    if (localStream) {
        const audioTrack = localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            isAudioOn = audioTrack.enabled;
            updateAudioButton();
        }
    }
}

function toggleRemoteAudio() {
    isRemoteAudioOn = !isRemoteAudioOn;
    updateRemoteAudioButton();
    
    const message = isRemoteAudioOn ? 'Remote audio unmuted' : 'Remote audio muted';
    showNotification(message);
}

function updateVideoButton() {
    const icon = toggleVideoBtn.querySelector('i');
    if (isVideoOn) {
        icon.className = 'fas fa-video';
        toggleVideoBtn.style.background = '#667eea';
    } else {
        icon.className = 'fas fa-video-slash';
        toggleVideoBtn.style.background = '#dc3545';
    }
}

function updateAudioButton() {
    const icon = toggleAudioBtn.querySelector('i');
    if (isAudioOn) {
        icon.className = 'fas fa-microphone';
        toggleAudioBtn.style.background = '#667eea';
    } else {
        icon.className = 'fas fa-microphone-slash';
        toggleAudioBtn.style.background = '#dc3545';
    }
}

function updateRemoteAudioButton() {
    const icon = muteRemoteBtn.querySelector('i');
    if (isRemoteAudioOn) {
        icon.className = 'fas fa-volume-up';
        muteRemoteBtn.style.background = '#667eea';
    } else {
        icon.className = 'fas fa-volume-mute';
        muteRemoteBtn.style.background = '#dc3545';
    }
}

function updateCallStatus(text, status) {
    callStatusText.textContent = text;
    callStatus.className = `status-dot ${status}`;
}

function startCallTimer() {
    callTimerInterval = setInterval(() => {
        if (callStartTime) {
            const elapsed = Date.now() - callStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            callTimer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }, 1000);
}

function stopCallTimer() {
    if (callTimerInterval) {
        clearInterval(callTimerInterval);
        callTimerInterval = null;
    }
    callTimer.textContent = '00:00';
}

function simulateRemoteVideo() {
    // Show remote video placeholder with connected state
    remotePlaceholder.innerHTML = `
        <i class="fas fa-user-check"></i>
        <p>Sign Language User Connected</p>
    `;
    
    // Simulate video feed after a moment
    setTimeout(() => {
        remoteVideo.style.display = 'block';
        remotePlaceholder.style.display = 'none';
        
        // Create a colored background to simulate video
        remoteVideo.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
    }, 1000);
}

function startSignLanguageSimulation() {
    // Simulate receiving sign language translations every 5-10 seconds
    const simulateTranslation = () => {
        if (isConnected) {
            const translation = signLanguageTranslations[Math.floor(Math.random() * signLanguageTranslations.length)];
            addTranslation(translation);
            showGestureDetection();
            
            // Schedule next translation
            setTimeout(simulateTranslation, 5000 + Math.random() * 5000);
        }
    };
    
    // Start first translation after 3 seconds
    setTimeout(simulateTranslation, 3000);
}

function addTranslation(text) {
    // Remove placeholder if it exists
    const placeholder = translationDisplay.querySelector('.translation-placeholder');
    if (placeholder) {
        placeholder.remove();
    }
    
    // Create translation message
    const message = document.createElement('div');
    message.className = 'translation-message';
    
    const timestamp = new Date().toLocaleTimeString();
    message.innerHTML = `
        <div class="timestamp">${timestamp}</div>
        <div class="text">${text}</div>
    `;
    
    translationDisplay.appendChild(message);
    translationDisplay.scrollTop = translationDisplay.scrollHeight;
}

function showGestureDetection() {
    remoteGestureOverlay.classList.add('show');
    setTimeout(() => {
        remoteGestureOverlay.classList.remove('show');
    }, 2000);
}

function sendTextMessage() {
    const text = responseText.value.trim();
    if (text && isConnected) {
        // Simulate sending text message
        showNotification(`Text sent: "${text}"`);
        responseText.value = '';
        
        // Add to translation display as your response
        const message = document.createElement('div');
        message.className = 'translation-message';
        message.style.borderLeftColor = '#28a745';
        
        const timestamp = new Date().toLocaleTimeString();
        message.innerHTML = `
            <div class="timestamp">You - ${timestamp}</div>
            <div class="text">${text}</div>
        `;
        
        translationDisplay.appendChild(message);
        translationDisplay.scrollTop = translationDisplay.scrollHeight;
    }
}

function convertTextToSign() {
    const text = responseText.value.trim();
    if (text) {
        showNotification(`Converting "${text}" to sign language...`);
        // Simulate conversion process
        setTimeout(() => {
            showNotification('Text converted to sign language successfully!');
        }, 2000);
    }
}

function toggleGestureRecording() {
    gestureRecording = !gestureRecording;
    
    if (gestureRecording) {
        recordGestureBtn.innerHTML = '<i class="fas fa-stop"></i> Stop Recording';
        recordGestureBtn.style.background = '#dc3545';
        sendGestureBtn.disabled = true;
        showNotification('Recording gesture... Make your sign now.');
        
        // Simulate recording for 3 seconds
        setTimeout(() => {
            gestureRecording = false;
            recordGestureBtn.innerHTML = '<i class="fas fa-record-vinyl"></i> Record Gesture';
            recordGestureBtn.style.background = '#667eea';
            sendGestureBtn.disabled = false;
            showNotification('Gesture recorded successfully!');
        }, 3000);
    }
}

function sendGestureMessage() {
    if (isConnected) {
        showNotification('Gesture sent successfully!');
        sendGestureBtn.disabled = true;
        
        // Re-enable after a moment
        setTimeout(() => {
            sendGestureBtn.disabled = false;
        }, 2000);
    }
}

function switchResponseMode() {
    const selectedMode = document.querySelector('input[name="responseMode"]:checked').value;
    
    if (selectedMode === 'text') {
        textResponse.classList.remove('hidden');
        gestureResponse.classList.add('hidden');
    } else {
        textResponse.classList.add('hidden');
        gestureResponse.classList.remove('hidden');
    }
}

function clearTranslations() {
    translationDisplay.innerHTML = `
        <div class="translation-placeholder">
            <i class="fas fa-comments"></i>
            <p>Sign language translations will appear here</p>
        </div>
    `;
    showNotification('Translation history cleared.');
}

function saveTranslations() {
    const messages = translationDisplay.querySelectorAll('.translation-message');
    if (messages.length === 0) {
        showNotification('No translations to save.');
        return;
    }
    
    let content = 'Sign Language Call Transcript\n';
    content += '================================\n\n';
    
    messages.forEach(message => {
        const timestamp = message.querySelector('.timestamp').textContent;
        const text = message.querySelector('.text').textContent;
        content += `${timestamp}\n${text}\n\n`;
    });
    
    // Create and download file
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sign-language-call-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    
    showNotification('Translation history saved successfully!');
}

function showNotification(message) {
    toastMessage.textContent = message;
    notificationToast.classList.add('show');
    
    setTimeout(() => {
        notificationToast.classList.remove('show');
    }, 3000);
}

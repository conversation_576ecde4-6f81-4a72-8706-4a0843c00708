const nodemailer = require('nodemailer');

// Email templates
const emailTemplates = {
    verification: {
        subject: 'Verify Your Email - Sign Language Call',
        html: (data) => `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Email Verification</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🤝 Sign Language Call</h1>
                        <p>Welcome to our platform!</p>
                    </div>
                    <div class="content">
                        <h2>Hi ${data.name}!</h2>
                        <p>Thank you for registering with Sign Language Call. To complete your registration, please verify your email address by clicking the button below:</p>
                        
                        <div style="text-align: center;">
                            <a href="${data.verificationUrl}" class="button">Verify Email Address</a>
                        </div>
                        
                        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                        <p style="word-break: break-all; color: #667eea;">${data.verificationUrl}</p>
                        
                        <p>This verification link will expire in 24 hours for security reasons.</p>
                        
                        <p>If you didn't create an account with us, please ignore this email.</p>
                        
                        <p>Best regards,<br>The Sign Language Call Team</p>
                    </div>
                    <div class="footer">
                        <p>© 2024 Sign Language Call. All rights reserved.</p>
                        <p>This is an automated email. Please do not reply to this message.</p>
                    </div>
                </div>
            </body>
            </html>
        `
    },
    
    passwordReset: {
        subject: 'Password Reset - Sign Language Call',
        html: (data) => `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Password Reset</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .button { display: inline-block; background: #e74c3c; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔒 Password Reset</h1>
                        <p>Sign Language Call</p>
                    </div>
                    <div class="content">
                        <h2>Hi ${data.name}!</h2>
                        <p>We received a request to reset your password for your Sign Language Call account.</p>
                        
                        <div class="warning">
                            <strong>⚠️ Security Notice:</strong> If you didn't request this password reset, please ignore this email and your password will remain unchanged.
                        </div>
                        
                        <p>To reset your password, click the button below:</p>
                        
                        <div style="text-align: center;">
                            <a href="${data.resetUrl}" class="button">Reset Password</a>
                        </div>
                        
                        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                        <p style="word-break: break-all; color: #667eea;">${data.resetUrl}</p>
                        
                        <p><strong>Important:</strong> This password reset link will expire in 10 minutes for security reasons.</p>
                        
                        <p>After clicking the link, you'll be able to create a new password for your account.</p>
                        
                        <p>Best regards,<br>The Sign Language Call Team</p>
                    </div>
                    <div class="footer">
                        <p>© 2024 Sign Language Call. All rights reserved.</p>
                        <p>This is an automated email. Please do not reply to this message.</p>
                    </div>
                </div>
            </body>
            </html>
        `
    },
    
    callInvitation: {
        subject: 'Call Invitation - Sign Language Call',
        html: (data) => `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Call Invitation</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .button { display: inline-block; background: #27ae60; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .call-details { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #667eea; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>📞 Call Invitation</h1>
                        <p>Sign Language Call</p>
                    </div>
                    <div class="content">
                        <h2>Hi ${data.recipientName}!</h2>
                        <p><strong>${data.hostName}</strong> has invited you to join a sign language call.</p>
                        
                        <div class="call-details">
                            <h3>Call Details:</h3>
                            <p><strong>Room:</strong> ${data.roomName}</p>
                            <p><strong>Host:</strong> ${data.hostName}</p>
                            <p><strong>Scheduled Time:</strong> ${data.scheduledTime || 'Now'}</p>
                            <p><strong>Languages:</strong> ${data.languages ? data.languages.join(', ') : 'Not specified'}</p>
                            ${data.description ? `<p><strong>Description:</strong> ${data.description}</p>` : ''}
                        </div>
                        
                        <div style="text-align: center;">
                            <a href="${data.joinUrl}" class="button">Join Call</a>
                        </div>
                        
                        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                        <p style="word-break: break-all; color: #667eea;">${data.joinUrl}</p>
                        
                        <p>Make sure you have a stable internet connection and your camera/microphone are working properly.</p>
                        
                        <p>Best regards,<br>The Sign Language Call Team</p>
                    </div>
                    <div class="footer">
                        <p>© 2024 Sign Language Call. All rights reserved.</p>
                        <p>This is an automated email. Please do not reply to this message.</p>
                    </div>
                </div>
            </body>
            </html>
        `
    }
};

// Create transporter
const createTransporter = () => {
    return nodemailer.createTransporter({
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        secure: process.env.EMAIL_PORT == 465, // true for 465, false for other ports
        auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASS
        },
        tls: {
            rejectUnauthorized: false
        }
    });
};

// Send email function
const sendEmail = async (options) => {
    try {
        const transporter = createTransporter();
        
        // Get template
        const template = emailTemplates[options.template];
        if (!template) {
            throw new Error(`Email template '${options.template}' not found`);
        }
        
        // Prepare email options
        const mailOptions = {
            from: {
                name: 'Sign Language Call',
                address: process.env.EMAIL_USER
            },
            to: options.to,
            subject: options.subject || template.subject,
            html: template.html(options.data || {})
        };
        
        // Add CC and BCC if provided
        if (options.cc) mailOptions.cc = options.cc;
        if (options.bcc) mailOptions.bcc = options.bcc;
        
        // Send email
        const info = await transporter.sendMail(mailOptions);
        
        console.log('Email sent successfully:', {
            messageId: info.messageId,
            to: options.to,
            subject: mailOptions.subject
        });
        
        return {
            success: true,
            messageId: info.messageId
        };
        
    } catch (error) {
        console.error('Email sending failed:', error);
        throw new Error(`Failed to send email: ${error.message}`);
    }
};

// Send bulk emails
const sendBulkEmail = async (recipients, template, data) => {
    const results = [];
    
    for (const recipient of recipients) {
        try {
            const result = await sendEmail({
                to: recipient.email,
                template: template,
                data: {
                    ...data,
                    name: recipient.name,
                    ...recipient.customData
                }
            });
            
            results.push({
                email: recipient.email,
                success: true,
                messageId: result.messageId
            });
        } catch (error) {
            results.push({
                email: recipient.email,
                success: false,
                error: error.message
            });
        }
    }
    
    return results;
};

// Verify email configuration
const verifyEmailConfig = async () => {
    try {
        const transporter = createTransporter();
        await transporter.verify();
        console.log('✅ Email configuration verified successfully');
        return true;
    } catch (error) {
        console.error('❌ Email configuration verification failed:', error.message);
        return false;
    }
};

module.exports = {
    sendEmail,
    sendBulkEmail,
    verifyEmailConfig,
    emailTemplates
};

const express = require('express');
const { body, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');
const Call = require('../models/Call');
const Room = require('../models/Room');
const { asyncHandler, handleValidationError } = require('../middleware/errorHandler');

const router = express.Router();

// @route   POST /api/calls
// @desc    Initiate a new call
// @access  Private
router.post('/', [
    body('roomId')
        .notEmpty()
        .withMessage('Room ID is required'),
    body('participants')
        .isArray({ min: 1 })
        .withMessage('At least one participant is required')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json(handleValidationError(errors));
    }

    const { roomId, participants } = req.body;

    // Validate room exists
    const room = await Room.findOne({ roomId });
    if (!room) {
        return res.status(404).json({
            success: false,
            message: 'Room not found'
        });
    }

    // Check if user is authorized to start call
    const isHost = room.host.toString() === req.user._id.toString();
    const isParticipant = room.participants.some(
        p => p.user.toString() === req.user._id.toString()
    );

    if (!isHost && !isParticipant) {
        return res.status(403).json({
            success: false,
            message: 'Not authorized to start call in this room'
        });
    }

    // Check if there's already an active call in the room
    const existingCall = await Call.findOne({
        room: room._id,
        status: { $in: ['initiated', 'ringing', 'connected'] }
    });

    if (existingCall) {
        return res.status(409).json({
            success: false,
            message: 'There is already an active call in this room'
        });
    }

    // Create call
    const call = await Call.create({
        callId: uuidv4(),
        room: room._id,
        participants: participants.map(p => ({
            user: p.userId,
            role: p.role || 'participant'
        })),
        status: 'initiated'
    });

    // Update room status
    room.status = 'active';
    await room.save();

    // Populate call data
    const populatedCall = await Call.findById(call._id)
        .populate('room', 'roomId name')
        .populate('participants.user', 'name profile.avatar');

    res.status(201).json({
        success: true,
        message: 'Call initiated successfully',
        data: {
            call: populatedCall
        }
    });
}));

// @route   PUT /api/calls/:callId/start
// @desc    Start a call
// @access  Private
router.put('/:callId/start', asyncHandler(async (req, res) => {
    const { callId } = req.params;

    const call = await Call.findOne({ callId })
        .populate('room', 'roomId name host')
        .populate('participants.user', 'name profile.avatar');

    if (!call) {
        return res.status(404).json({
            success: false,
            message: 'Call not found'
        });
    }

    // Check if user is authorized to start call
    const isHost = call.room.host.toString() === req.user._id.toString();
    const isParticipant = call.participants.some(
        p => p.user._id.toString() === req.user._id.toString()
    );

    if (!isHost && !isParticipant) {
        return res.status(403).json({
            success: false,
            message: 'Not authorized to start this call'
        });
    }

    // Start call
    await call.startCall();

    res.json({
        success: true,
        message: 'Call started successfully',
        data: {
            call
        }
    });
}));

// @route   PUT /api/calls/:callId/end
// @desc    End a call
// @access  Private
router.put('/:callId/end', asyncHandler(async (req, res) => {
    const { callId } = req.params;

    const call = await Call.findOne({ callId })
        .populate('room', 'roomId name host');

    if (!call) {
        return res.status(404).json({
            success: false,
            message: 'Call not found'
        });
    }

    // Check if user is authorized to end call
    const isHost = call.room.host.toString() === req.user._id.toString();
    const isParticipant = call.participants.some(
        p => p.user._id.toString() === req.user._id.toString()
    );

    if (!isHost && !isParticipant) {
        return res.status(403).json({
            success: false,
            message: 'Not authorized to end this call'
        });
    }

    // End call
    await call.endCall();

    // Update room status
    const room = await Room.findById(call.room._id);
    if (room) {
        await room.endCall();
    }

    // Calculate cost
    call.calculateCost();
    await call.save();

    res.json({
        success: true,
        message: 'Call ended successfully',
        data: {
            call,
            duration: call.duration,
            cost: call.billing.cost
        }
    });
}));

// @route   POST /api/calls/:callId/join
// @desc    Join a call
// @access  Private
router.post('/:callId/join', asyncHandler(async (req, res) => {
    const { callId } = req.params;
    const { role = 'participant' } = req.body;

    const call = await Call.findOne({ callId })
        .populate('room', 'roomId name')
        .populate('participants.user', 'name profile.avatar');

    if (!call) {
        return res.status(404).json({
            success: false,
            message: 'Call not found'
        });
    }

    // Check if call is active
    if (call.status !== 'connected') {
        return res.status(400).json({
            success: false,
            message: 'Call is not active'
        });
    }

    // Add participant to call
    try {
        await call.addParticipant(req.user._id, role);

        const updatedCall = await Call.findOne({ callId })
            .populate('room', 'roomId name')
            .populate('participants.user', 'name profile.avatar');

        res.json({
            success: true,
            message: 'Successfully joined call',
            data: {
                call: updatedCall
            }
        });
    } catch (error) {
        return res.status(400).json({
            success: false,
            message: error.message
        });
    }
}));

// @route   POST /api/calls/:callId/leave
// @desc    Leave a call
// @access  Private
router.post('/:callId/leave', asyncHandler(async (req, res) => {
    const { callId } = req.params;

    const call = await Call.findOne({ callId });

    if (!call) {
        return res.status(404).json({
            success: false,
            message: 'Call not found'
        });
    }

    // Remove participant from call
    await call.removeParticipant(req.user._id);

    res.json({
        success: true,
        message: 'Successfully left call'
    });
}));

// @route   GET /api/calls/:callId
// @desc    Get call details
// @access  Private
router.get('/:callId', asyncHandler(async (req, res) => {
    const { callId } = req.params;

    const call = await Call.findOne({ callId })
        .populate('room', 'roomId name')
        .populate('participants.user', 'name profile.avatar')
        .populate('transcription.speaker', 'name profile.avatar')
        .populate('messages.sender', 'name profile.avatar');

    if (!call) {
        return res.status(404).json({
            success: false,
            message: 'Call not found'
        });
    }

    // Check if user has access to call
    const isParticipant = call.participants.some(
        p => p.user._id.toString() === req.user._id.toString()
    );

    if (!isParticipant) {
        return res.status(403).json({
            success: false,
            message: 'Access denied to this call'
        });
    }

    res.json({
        success: true,
        data: {
            call
        }
    });
}));

// @route   GET /api/calls
// @desc    Get user's call history
// @access  Private
router.get('/', asyncHandler(async (req, res) => {
    const { status, page = 1, limit = 10, startDate, endDate } = req.query;

    // Build query
    const query = {
        'participants.user': req.user._id
    };

    if (status) {
        query.status = status;
    }

    if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate);
        if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Pagination
    const skip = (page - 1) * limit;

    const calls = await Call.find(query)
        .populate('room', 'roomId name')
        .populate('participants.user', 'name profile.avatar')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit));

    const total = await Call.countDocuments(query);

    res.json({
        success: true,
        data: {
            calls,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        }
    });
}));

// @route   POST /api/calls/:callId/transcription
// @desc    Add transcription to call
// @access  Private
router.post('/:callId/transcription', [
    body('content')
        .notEmpty()
        .withMessage('Transcription content is required'),
    body('type')
        .isIn(['speech', 'sign_language', 'text', 'gesture'])
        .withMessage('Invalid transcription type'),
    body('confidence')
        .optional()
        .isFloat({ min: 0, max: 1 })
        .withMessage('Confidence must be between 0 and 1')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json(handleValidationError(errors));
    }

    const { callId } = req.params;
    const { content, type, confidence = 1.0, language = 'ASL' } = req.body;

    const call = await Call.findOne({ callId });

    if (!call) {
        return res.status(404).json({
            success: false,
            message: 'Call not found'
        });
    }

    // Check if user is participant
    const isParticipant = call.participants.some(
        p => p.user._id.toString() === req.user._id.toString()
    );

    if (!isParticipant) {
        return res.status(403).json({
            success: false,
            message: 'Access denied'
        });
    }

    // Add transcription
    await call.addTranscription(req.user._id, content, type, confidence, language);

    res.json({
        success: true,
        message: 'Transcription added successfully'
    });
}));

// @route   POST /api/calls/:callId/rating
// @desc    Rate call quality
// @access  Private
router.post('/:callId/rating', [
    body('rating')
        .isInt({ min: 1, max: 5 })
        .withMessage('Rating must be between 1 and 5'),
    body('feedback')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('Feedback cannot be more than 1000 characters')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json(handleValidationError(errors));
    }

    const { callId } = req.params;
    const { rating, feedback } = req.body;

    const call = await Call.findOne({ callId });

    if (!call) {
        return res.status(404).json({
            success: false,
            message: 'Call not found'
        });
    }

    // Check if user is participant
    const isParticipant = call.participants.some(
        p => p.user._id.toString() === req.user._id.toString()
    );

    if (!isParticipant) {
        return res.status(403).json({
            success: false,
            message: 'Access denied'
        });
    }

    // Check if user already rated
    const existingRating = call.qualityMetrics.userSatisfaction.find(
        r => r.user.toString() === req.user._id.toString()
    );

    if (existingRating) {
        return res.status(400).json({
            success: false,
            message: 'You have already rated this call'
        });
    }

    // Add rating
    call.qualityMetrics.userSatisfaction.push({
        user: req.user._id,
        rating,
        feedback,
        submittedAt: new Date()
    });

    await call.save();

    res.json({
        success: true,
        message: 'Rating submitted successfully'
    });
}));

module.exports = router;

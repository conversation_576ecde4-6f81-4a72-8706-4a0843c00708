// DOM Elements
const tabButtons = document.querySelectorAll('.tab-btn');
const authForms = document.querySelectorAll('.auth-form');
const passwordToggles = document.querySelectorAll('.password-toggle');
const loginForm = document.getElementById('loginForm');
const registerForm = document.getElementById('registerForm');
const passwordInput = document.getElementById('registerPassword');
const confirmPasswordInput = document.getElementById('confirmPassword');
const strengthBar = document.querySelector('.strength-bar');
const strengthText = document.querySelector('.strength-text');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializePasswordToggles();
    initializeFormValidation();
    initializePasswordStrength();
    initializeAnimations();
    createFloatingElements();
    createProfessionalBackground();
});

// Tab switching functionality
function initializeTabs() {
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            switchTab(targetTab);
        });
    });
}

function switchTab(targetTab) {
    // Update tab buttons
    tabButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-tab="${targetTab}"]`).classList.add('active');

    // Update forms
    authForms.forEach(form => form.classList.remove('active'));
    document.getElementById(`${targetTab}-form`).classList.add('active');

    // Clear any existing errors
    clearAllErrors();
}

// Password visibility toggle
function initializePasswordToggles() {
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', () => {
            const targetId = toggle.getAttribute('data-target');
            const targetInput = document.getElementById(targetId);
            const icon = toggle.querySelector('i');

            if (targetInput.type === 'password') {
                targetInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                targetInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
}

// Form validation
function initializeFormValidation() {
    // Login form validation
    loginForm.addEventListener('submit', handleLoginSubmit);

    // Register form validation
    registerForm.addEventListener('submit', handleRegisterSubmit);

    // Real-time validation
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('blur', () => validateField(input));
        input.addEventListener('input', () => clearFieldError(input));
    });
}

function handleLoginSubmit(e) {
    e.preventDefault();

    const email = document.getElementById('loginEmail');
    const password = document.getElementById('loginPassword');

    let isValid = true;

    // Validate email
    if (!validateEmail(email.value)) {
        showFieldError(email, 'Please enter a valid email address');
        isValid = false;
    }

    // Validate password
    if (!password.value.trim()) {
        showFieldError(password, 'Password is required');
        isValid = false;
    }

    if (isValid) {
        simulateLogin(email.value, password.value);
    }
}

function handleRegisterSubmit(e) {
    e.preventDefault();

    const name = document.getElementById('registerName');
    const email = document.getElementById('registerEmail');
    const password = document.getElementById('registerPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    const agreeTerms = document.getElementById('agreeTerms');

    let isValid = true;

    // Validate name
    if (!name.value.trim() || name.value.trim().length < 2) {
        showFieldError(name, 'Please enter your full name (at least 2 characters)');
        isValid = false;
    }

    // Validate email
    if (!validateEmail(email.value)) {
        showFieldError(email, 'Please enter a valid email address');
        isValid = false;
    }

    // Validate password
    const passwordStrength = checkPasswordStrength(password.value);
    if (passwordStrength.score < 2) {
        showFieldError(password, 'Password is too weak. Please choose a stronger password');
        isValid = false;
    }

    // Validate password confirmation
    if (password.value !== confirmPassword.value) {
        showFieldError(confirmPassword, 'Passwords do not match');
        isValid = false;
    }

    // Validate terms agreement
    if (!agreeTerms.checked) {
        alert('Please agree to the Terms & Conditions');
        isValid = false;
    }

    if (isValid) {
        simulateRegistration(name.value, email.value, password.value);
    }
}

// Field validation functions
function validateField(input) {
    const value = input.value.trim();

    switch (input.type) {
        case 'email':
            if (!validateEmail(value)) {
                showFieldError(input, 'Please enter a valid email address');
                return false;
            }
            break;
        case 'password':
            if (input.id === 'confirmPassword') {
                const password = document.getElementById('registerPassword').value;
                if (value !== password) {
                    showFieldError(input, 'Passwords do not match');
                    return false;
                }
            }
            break;
        case 'text':
            if (input.id === 'registerName' && value.length < 2) {
                showFieldError(input, 'Name must be at least 2 characters long');
                return false;
            }
            break;
    }

    clearFieldError(input);
    return true;
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showFieldError(input, message) {
    const inputGroup = input.closest('.input-group');
    const errorElement = input.closest('.form-group').querySelector('.error-message');

    inputGroup.classList.add('error');
    inputGroup.classList.remove('success');
    errorElement.textContent = message;
}

function clearFieldError(input) {
    const inputGroup = input.closest('.input-group');
    const errorElement = input.closest('.form-group').querySelector('.error-message');

    inputGroup.classList.remove('error');
    if (input.value.trim()) {
        inputGroup.classList.add('success');
    }
    errorElement.textContent = '';
}

function clearAllErrors() {
    const errorMessages = document.querySelectorAll('.error-message');
    const inputGroups = document.querySelectorAll('.input-group');

    errorMessages.forEach(error => error.textContent = '');
    inputGroups.forEach(group => {
        group.classList.remove('error', 'success');
    });
}

// Password strength checker
function initializePasswordStrength() {
    if (passwordInput) {
        passwordInput.addEventListener('input', () => {
            const strength = checkPasswordStrength(passwordInput.value);
            updatePasswordStrengthUI(strength);
        });
    }
}

function checkPasswordStrength(password) {
    let score = 0;
    let feedback = [];

    // Length check
    if (password.length >= 8) score++;
    else feedback.push('at least 8 characters');

    // Lowercase check
    if (/[a-z]/.test(password)) score++;
    else feedback.push('lowercase letters');

    // Uppercase check
    if (/[A-Z]/.test(password)) score++;
    else feedback.push('uppercase letters');

    // Number check
    if (/\d/.test(password)) score++;
    else feedback.push('numbers');

    // Special character check
    if (/[^A-Za-z0-9]/.test(password)) score++;
    else feedback.push('special characters');

    const levels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const level = Math.min(score, 4);

    return {
        score: score,
        level: levels[level],
        feedback: feedback
    };
}

function updatePasswordStrengthUI(strength) {
    const classes = ['weak', 'weak', 'fair', 'good', 'strong'];
    const colors = ['#e74c3c', '#e74c3c', '#f39c12', '#f1c40f', '#27ae60'];

    strengthBar.className = 'strength-bar';
    if (strength.score > 0) {
        strengthBar.classList.add(classes[Math.min(strength.score - 1, 4)]);
    }

    strengthText.textContent = strength.level;
    strengthText.style.color = colors[Math.min(strength.score, 4)];
}

// Simulate login/registration (replace with actual API calls)
function simulateLogin(email, password) {
    const button = loginForm.querySelector('.auth-btn');
    button.classList.add('loading');
    button.innerHTML = '<span>Signing In...</span>';

    setTimeout(() => {
        button.classList.remove('loading');
        button.innerHTML = '<span>Sign In</span><i class="fas fa-arrow-right"></i>';
        alert(`Login successful!\nEmail: ${email}`);
        // Redirect to dashboard or main app
    }, 2000);
}

function simulateRegistration(name, email, password) {
    const button = registerForm.querySelector('.auth-btn');
    button.classList.add('loading');
    button.innerHTML = '<span>Creating Account...</span>';

    setTimeout(() => {
        button.classList.remove('loading');
        button.innerHTML = '<span>Create Account</span><i class="fas fa-arrow-right"></i>';
        alert(`Registration successful!\nName: ${name}\nEmail: ${email}`);
        // Switch to login tab or redirect
        switchTab('login');
    }, 2000);
}

// Enhanced animations and interactions
function initializeAnimations() {
    // Add input focus animations
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('focus', handleInputFocus);
        input.addEventListener('blur', handleInputBlur);
    });

    // Add button click ripple effect
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', createRippleEffect);
    });
}



function handleInputFocus(e) {
    const formGroup = e.target.closest('.form-group');
    formGroup.style.transform = 'scale(1.02)';
    formGroup.style.zIndex = '10';
}

function handleInputBlur(e) {
    const formGroup = e.target.closest('.form-group');
    formGroup.style.transform = 'scale(1)';
    formGroup.style.zIndex = '1';
}

function createRippleEffect(e) {
    const button = e.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    const ripple = document.createElement('span');
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
    `;

    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

function createFloatingElements() {
    // Create additional floating elements for enhanced visual appeal
    const container = document.querySelector('.auth-container');

    for (let i = 0; i < 3; i++) {
        const floatingElement = document.createElement('div');
        floatingElement.className = 'floating-element';
        floatingElement.style.cssText = `
            position: absolute;
            width: ${Math.random() * 6 + 4}px;
            height: ${Math.random() * 6 + 4}px;
            background: rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            animation: floatAround ${Math.random() * 10 + 15}s infinite ease-in-out;
            animation-delay: ${Math.random() * 5}s;
            pointer-events: none;
            z-index: -1;
        `;

        container.appendChild(floatingElement);
    }
}

// Add CSS for floating elements animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }

    @keyframes floatAround {
        0%, 100% {
            transform: translate(0, 0) rotate(0deg);
        }
        25% {
            transform: translate(30px, -30px) rotate(90deg);
        }
        50% {
            transform: translate(-20px, 20px) rotate(180deg);
        }
        75% {
            transform: translate(-30px, -10px) rotate(270deg);
        }
    }
`;
document.head.appendChild(style);

// Create professional background elements
function createProfessionalBackground() {
    // Create animated tech lines
    createTechLines();

    // Create floating geometric shapes
    createGeometricShapes();

    // Create subtle data visualization elements
    createDataElements();
}

function createTechLines() {
    const linesContainer = document.createElement('div');
    linesContainer.className = 'tech-lines';
    linesContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 0;
        overflow: hidden;
    `;

    // Create multiple animated lines
    for (let i = 0; i < 6; i++) {
        const line = document.createElement('div');
        line.style.cssText = `
            position: absolute;
            width: 1px;
            height: 100px;
            background: linear-gradient(180deg, transparent, rgba(102, 126, 234, 0.3), transparent);
            animation: techLineMove ${15 + Math.random() * 10}s linear infinite;
            animation-delay: ${Math.random() * 5}s;
            left: ${Math.random() * 100}%;
            top: -100px;
        `;
        linesContainer.appendChild(line);
    }

    document.body.appendChild(linesContainer);
}

function createGeometricShapes() {
    const shapesContainer = document.createElement('div');
    shapesContainer.className = 'geometric-shapes';
    shapesContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 0;
    `;

    // Create hexagons, triangles, and squares
    const shapes = ['hexagon', 'triangle', 'square'];

    for (let i = 0; i < 8; i++) {
        const shape = document.createElement('div');
        const shapeType = shapes[Math.floor(Math.random() * shapes.length)];
        const size = Math.random() * 30 + 20;

        shape.className = `geometric-shape ${shapeType}`;
        shape.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: geometricFloat ${20 + Math.random() * 15}s ease-in-out infinite;
            animation-delay: ${Math.random() * 5}s;
            opacity: 0.1;
        `;

        if (shapeType === 'hexagon') {
            shape.style.background = 'rgba(102, 126, 234, 0.2)';
            shape.style.clipPath = 'polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%)';
        } else if (shapeType === 'triangle') {
            shape.style.background = 'rgba(118, 75, 162, 0.2)';
            shape.style.clipPath = 'polygon(50% 0%, 0% 100%, 100% 100%)';
        } else {
            shape.style.background = 'rgba(240, 147, 251, 0.2)';
            shape.style.borderRadius = '4px';
            shape.style.transform = 'rotate(45deg)';
        }

        shapesContainer.appendChild(shape);
    }

    document.body.appendChild(shapesContainer);
}

function createDataElements() {
    const dataContainer = document.createElement('div');
    dataContainer.className = 'data-elements';
    dataContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 0;
    `;

    // Create subtle data bars and dots
    for (let i = 0; i < 12; i++) {
        const element = document.createElement('div');
        const isBar = Math.random() > 0.5;

        if (isBar) {
            element.style.cssText = `
                position: absolute;
                width: 2px;
                height: ${Math.random() * 40 + 10}px;
                background: rgba(255, 255, 255, 0.1);
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: dataBarPulse ${3 + Math.random() * 4}s ease-in-out infinite;
                animation-delay: ${Math.random() * 3}s;
            `;
        } else {
            element.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: rgba(102, 126, 234, 0.3);
                border-radius: 50%;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: dataDotBlink ${2 + Math.random() * 3}s ease-in-out infinite;
                animation-delay: ${Math.random() * 2}s;
            `;
        }

        dataContainer.appendChild(element);
    }

    document.body.appendChild(dataContainer);
}

// Add additional CSS animations for professional elements
const professionalStyle = document.createElement('style');
professionalStyle.textContent = `
    @keyframes techLineMove {
        0% {
            transform: translateY(-100px);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(calc(100vh + 100px));
            opacity: 0;
        }
    }

    @keyframes geometricFloat {
        0%, 100% {
            transform: translate(0, 0) rotate(0deg);
            opacity: 0.1;
        }
        25% {
            transform: translate(20px, -20px) rotate(90deg);
            opacity: 0.2;
        }
        50% {
            transform: translate(-10px, 10px) rotate(180deg);
            opacity: 0.15;
        }
        75% {
            transform: translate(-20px, -10px) rotate(270deg);
            opacity: 0.1;
        }
    }

    @keyframes dataBarPulse {
        0%, 100% {
            opacity: 0.1;
            transform: scaleY(1);
        }
        50% {
            opacity: 0.3;
            transform: scaleY(1.5);
        }
    }

    @keyframes dataDotBlink {
        0%, 100% {
            opacity: 0.2;
            transform: scale(1);
        }
        50% {
            opacity: 0.6;
            transform: scale(1.5);
        }
    }
`;
document.head.appendChild(professionalStyle);

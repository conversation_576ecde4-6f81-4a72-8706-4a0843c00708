// DOM Elements
const tabButtons = document.querySelectorAll('.tab-btn');
const authForms = document.querySelectorAll('.auth-form');
const passwordToggles = document.querySelectorAll('.password-toggle');
const loginForm = document.getElementById('loginForm');
const registerForm = document.getElementById('registerForm');
const passwordInput = document.getElementById('registerPassword');
const confirmPasswordInput = document.getElementById('confirmPassword');
const strengthBar = document.querySelector('.strength-bar');
const strengthText = document.querySelector('.strength-text');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializePasswordToggles();
    initializeFormValidation();
    initializePasswordStrength();
});

// Tab switching functionality
function initializeTabs() {
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            switchTab(targetTab);
        });
    });
}

function switchTab(targetTab) {
    // Update tab buttons
    tabButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-tab="${targetTab}"]`).classList.add('active');
    
    // Update forms
    authForms.forEach(form => form.classList.remove('active'));
    document.getElementById(`${targetTab}-form`).classList.add('active');
    
    // Clear any existing errors
    clearAllErrors();
}

// Password visibility toggle
function initializePasswordToggles() {
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', () => {
            const targetId = toggle.getAttribute('data-target');
            const targetInput = document.getElementById(targetId);
            const icon = toggle.querySelector('i');
            
            if (targetInput.type === 'password') {
                targetInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                targetInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
}

// Form validation
function initializeFormValidation() {
    // Login form validation
    loginForm.addEventListener('submit', handleLoginSubmit);
    
    // Register form validation
    registerForm.addEventListener('submit', handleRegisterSubmit);
    
    // Real-time validation
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('blur', () => validateField(input));
        input.addEventListener('input', () => clearFieldError(input));
    });
}

function handleLoginSubmit(e) {
    e.preventDefault();
    
    const email = document.getElementById('loginEmail');
    const password = document.getElementById('loginPassword');
    
    let isValid = true;
    
    // Validate email
    if (!validateEmail(email.value)) {
        showFieldError(email, 'Please enter a valid email address');
        isValid = false;
    }
    
    // Validate password
    if (!password.value.trim()) {
        showFieldError(password, 'Password is required');
        isValid = false;
    }
    
    if (isValid) {
        simulateLogin(email.value, password.value);
    }
}

function handleRegisterSubmit(e) {
    e.preventDefault();
    
    const name = document.getElementById('registerName');
    const email = document.getElementById('registerEmail');
    const password = document.getElementById('registerPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    const agreeTerms = document.getElementById('agreeTerms');
    
    let isValid = true;
    
    // Validate name
    if (!name.value.trim() || name.value.trim().length < 2) {
        showFieldError(name, 'Please enter your full name (at least 2 characters)');
        isValid = false;
    }
    
    // Validate email
    if (!validateEmail(email.value)) {
        showFieldError(email, 'Please enter a valid email address');
        isValid = false;
    }
    
    // Validate password
    const passwordStrength = checkPasswordStrength(password.value);
    if (passwordStrength.score < 2) {
        showFieldError(password, 'Password is too weak. Please choose a stronger password');
        isValid = false;
    }
    
    // Validate password confirmation
    if (password.value !== confirmPassword.value) {
        showFieldError(confirmPassword, 'Passwords do not match');
        isValid = false;
    }
    
    // Validate terms agreement
    if (!agreeTerms.checked) {
        alert('Please agree to the Terms & Conditions');
        isValid = false;
    }
    
    if (isValid) {
        simulateRegistration(name.value, email.value, password.value);
    }
}

// Field validation functions
function validateField(input) {
    const value = input.value.trim();
    
    switch (input.type) {
        case 'email':
            if (!validateEmail(value)) {
                showFieldError(input, 'Please enter a valid email address');
                return false;
            }
            break;
        case 'password':
            if (input.id === 'confirmPassword') {
                const password = document.getElementById('registerPassword').value;
                if (value !== password) {
                    showFieldError(input, 'Passwords do not match');
                    return false;
                }
            }
            break;
        case 'text':
            if (input.id === 'registerName' && value.length < 2) {
                showFieldError(input, 'Name must be at least 2 characters long');
                return false;
            }
            break;
    }
    
    clearFieldError(input);
    return true;
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showFieldError(input, message) {
    const inputGroup = input.closest('.input-group');
    const errorElement = input.closest('.form-group').querySelector('.error-message');
    
    inputGroup.classList.add('error');
    inputGroup.classList.remove('success');
    errorElement.textContent = message;
}

function clearFieldError(input) {
    const inputGroup = input.closest('.input-group');
    const errorElement = input.closest('.form-group').querySelector('.error-message');
    
    inputGroup.classList.remove('error');
    if (input.value.trim()) {
        inputGroup.classList.add('success');
    }
    errorElement.textContent = '';
}

function clearAllErrors() {
    const errorMessages = document.querySelectorAll('.error-message');
    const inputGroups = document.querySelectorAll('.input-group');
    
    errorMessages.forEach(error => error.textContent = '');
    inputGroups.forEach(group => {
        group.classList.remove('error', 'success');
    });
}

// Password strength checker
function initializePasswordStrength() {
    if (passwordInput) {
        passwordInput.addEventListener('input', () => {
            const strength = checkPasswordStrength(passwordInput.value);
            updatePasswordStrengthUI(strength);
        });
    }
}

function checkPasswordStrength(password) {
    let score = 0;
    let feedback = [];
    
    // Length check
    if (password.length >= 8) score++;
    else feedback.push('at least 8 characters');
    
    // Lowercase check
    if (/[a-z]/.test(password)) score++;
    else feedback.push('lowercase letters');
    
    // Uppercase check
    if (/[A-Z]/.test(password)) score++;
    else feedback.push('uppercase letters');
    
    // Number check
    if (/\d/.test(password)) score++;
    else feedback.push('numbers');
    
    // Special character check
    if (/[^A-Za-z0-9]/.test(password)) score++;
    else feedback.push('special characters');
    
    const levels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const level = Math.min(score, 4);
    
    return {
        score: score,
        level: levels[level],
        feedback: feedback
    };
}

function updatePasswordStrengthUI(strength) {
    const classes = ['weak', 'weak', 'fair', 'good', 'strong'];
    const colors = ['#e74c3c', '#e74c3c', '#f39c12', '#f1c40f', '#27ae60'];
    
    strengthBar.className = 'strength-bar';
    if (strength.score > 0) {
        strengthBar.classList.add(classes[Math.min(strength.score - 1, 4)]);
    }
    
    strengthText.textContent = strength.level;
    strengthText.style.color = colors[Math.min(strength.score, 4)];
}

// Simulate login/registration (replace with actual API calls)
function simulateLogin(email, password) {
    const button = loginForm.querySelector('.auth-btn');
    button.classList.add('loading');
    button.innerHTML = '<span>Signing In...</span>';
    
    setTimeout(() => {
        button.classList.remove('loading');
        button.innerHTML = '<span>Sign In</span><i class="fas fa-arrow-right"></i>';
        alert(`Login successful!\nEmail: ${email}`);
        // Redirect to dashboard or main app
    }, 2000);
}

function simulateRegistration(name, email, password) {
    const button = registerForm.querySelector('.auth-btn');
    button.classList.add('loading');
    button.innerHTML = '<span>Creating Account...</span>';
    
    setTimeout(() => {
        button.classList.remove('loading');
        button.innerHTML = '<span>Create Account</span><i class="fas fa-arrow-right"></i>';
        alert(`Registration successful!\nName: ${name}\nEmail: ${email}`);
        // Switch to login tab or redirect
        switchTab('login');
    }, 2000);
}

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Name is required'],
        trim: true,
        maxlength: [50, 'Name cannot be more than 50 characters']
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        unique: true,
        lowercase: true,
        match: [
            /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
            'Please enter a valid email'
        ]
    },
    password: {
        type: String,
        required: [true, 'Password is required'],
        minlength: [6, 'Password must be at least 6 characters'],
        select: false
    },
    role: {
        type: String,
        enum: ['interpreter', 'sign_user', 'admin'],
        default: 'sign_user',
        required: true
    },
    profile: {
        avatar: {
            type: String,
            default: ''
        },
        bio: {
            type: String,
            maxlength: [500, 'Bio cannot be more than 500 characters']
        },
        languages: [{
            type: String,
            enum: ['ASL', 'BSL', 'ISL', 'JSL', 'CSL', 'FSL', 'GSL', 'Other']
        }],
        experience: {
            type: String,
            enum: ['beginner', 'intermediate', 'advanced', 'expert'],
            default: 'beginner'
        },
        certifications: [{
            name: String,
            issuer: String,
            date: Date,
            verified: {
                type: Boolean,
                default: false
            }
        }]
    },
    preferences: {
        videoQuality: {
            type: String,
            enum: ['low', 'medium', 'high', 'auto'],
            default: 'auto'
        },
        notifications: {
            email: {
                type: Boolean,
                default: true
            },
            push: {
                type: Boolean,
                default: true
            },
            callRequests: {
                type: Boolean,
                default: true
            }
        },
        privacy: {
            showOnline: {
                type: Boolean,
                default: true
            },
            allowRecording: {
                type: Boolean,
                default: false
            }
        }
    },
    status: {
        type: String,
        enum: ['online', 'offline', 'busy', 'away'],
        default: 'offline'
    },
    lastActive: {
        type: Date,
        default: Date.now
    },
    callHistory: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Call'
    }],
    isVerified: {
        type: Boolean,
        default: false
    },
    verificationToken: String,
    passwordResetToken: String,
    passwordResetExpires: Date,
    loginAttempts: {
        type: Number,
        default: 0
    },
    lockUntil: Date
}, {
    timestamps: true
});

// Indexes for better performance
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });
userSchema.index({ status: 1 });
userSchema.index({ 'profile.languages': 1 });

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
    return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    
    try {
        const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
        this.password = await bcrypt.hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
    if (!this.password) return false;
    return await bcrypt.compare(candidatePassword, this.password);
};

// Method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
    // If we have a previous lock that has expired, restart at 1
    if (this.lockUntil && this.lockUntil < Date.now()) {
        return this.updateOne({
            $unset: { lockUntil: 1 },
            $set: { loginAttempts: 1 }
        });
    }
    
    const updates = { $inc: { loginAttempts: 1 } };
    
    // Lock account after 5 failed attempts for 2 hours
    if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
        updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 };
    }
    
    return this.updateOne(updates);
};

// Method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
    return this.updateOne({
        $unset: { loginAttempts: 1, lockUntil: 1 }
    });
};

// Method to update last active
userSchema.methods.updateLastActive = function() {
    this.lastActive = new Date();
    return this.save();
};

// Method to get public profile
userSchema.methods.getPublicProfile = function() {
    const user = this.toObject();
    delete user.password;
    delete user.verificationToken;
    delete user.passwordResetToken;
    delete user.passwordResetExpires;
    delete user.loginAttempts;
    delete user.lockUntil;
    return user;
};

// Static method to find available interpreters
userSchema.statics.findAvailableInterpreters = function(languages = []) {
    const query = {
        role: 'interpreter',
        status: { $in: ['online', 'away'] },
        isVerified: true
    };
    
    if (languages.length > 0) {
        query['profile.languages'] = { $in: languages };
    }
    
    return this.find(query)
        .select('-password -verificationToken -passwordResetToken')
        .sort({ lastActive: -1 });
};

module.exports = mongoose.model('User', userSchema);
